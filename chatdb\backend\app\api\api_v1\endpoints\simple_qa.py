"""
简化版问答对API - 不依赖混合检索功能
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.api import deps
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

# ===== 请求/响应模型 =====

class SimpleQAPairCreate(BaseModel):
    """简化版问答对创建请求"""
    question: str
    sql: str
    connection_id: int
    difficulty_level: int = 3
    query_type: str = "SELECT"
    verified: bool = False

class SimpleQAPairResponse(BaseModel):
    """简化版问答对响应"""
    id: str
    question: str
    sql: str
    connection_id: int
    difficulty_level: int
    query_type: str
    verified: bool
    created_at: datetime

# ===== 简化版问答对存储 =====

# 内存存储（生产环境应该使用数据库）
qa_pairs_storage = []

def generate_simple_qa_id() -> str:
    """生成简单的问答对ID"""
    import uuid
    return str(uuid.uuid4())

def clean_simple_sql(sql: str) -> str:
    """清理SQL语句"""
    return sql.strip()

def extract_simple_tables_from_sql(sql: str) -> List[str]:
    """从SQL中提取表名（简化版）"""
    import re
    # 简单的表名提取逻辑
    tables = []
    sql_upper = sql.upper()
    
    # 查找FROM子句中的表名
    from_matches = re.findall(r'FROM\s+([a-zA-Z_][a-zA-Z0-9_]*)', sql_upper)
    tables.extend(from_matches)
    
    # 查找JOIN子句中的表名
    join_matches = re.findall(r'JOIN\s+([a-zA-Z_][a-zA-Z0-9_]*)', sql_upper)
    tables.extend(join_matches)
    
    return list(set(tables))  # 去重

# ===== API端点 =====

@router.post("/simple-qa-pairs/", response_model=Dict[str, Any])
async def create_simple_qa_pair(
    qa_create: SimpleQAPairCreate,
    db: Session = Depends(deps.get_db)
):
    """创建简化版问答对"""
    try:
        # 提取表名
        used_tables = extract_simple_tables_from_sql(qa_create.sql)
        
        # 创建问答对对象
        qa_pair = {
            "id": generate_simple_qa_id(),
            "question": qa_create.question,
            "sql": clean_simple_sql(qa_create.sql),
            "connection_id": qa_create.connection_id,
            "difficulty_level": qa_create.difficulty_level,
            "query_type": qa_create.query_type,
            "verified": qa_create.verified,
            "created_at": datetime.now(),
            "used_tables": used_tables
        }
        
        # 存储到内存（简化版）
        qa_pairs_storage.append(qa_pair)
        
        logger.info(f"Successfully created simple QA pair: {qa_pair['id']}")
        
        return {
            "status": "success",
            "qa_id": qa_pair["id"],
            "message": "问答对创建成功",
            "used_tables": used_tables
        }
        
    except Exception as e:
        logger.error(f"创建简化版问答对失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建问答对失败: {str(e)}")

@router.get("/simple-qa-pairs/", response_model=List[SimpleQAPairResponse])
async def get_simple_qa_pairs(
    connection_id: Optional[int] = None,
    db: Session = Depends(deps.get_db)
):
    """获取简化版问答对列表"""
    try:
        filtered_pairs = qa_pairs_storage
        
        if connection_id:
            filtered_pairs = [
                pair for pair in qa_pairs_storage 
                if pair["connection_id"] == connection_id
            ]
        
        return filtered_pairs
        
    except Exception as e:
        logger.error(f"获取问答对列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取问答对列表失败: {str(e)}")

@router.get("/simple-qa-pairs/stats", response_model=Dict[str, Any])
async def get_simple_qa_stats(
    connection_id: Optional[int] = None,
    db: Session = Depends(deps.get_db)
):
    """获取简化版问答对统计信息"""
    try:
        filtered_pairs = qa_pairs_storage
        
        if connection_id:
            filtered_pairs = [
                pair for pair in qa_pairs_storage 
                if pair["connection_id"] == connection_id
            ]
        
        total_count = len(filtered_pairs)
        verified_count = len([pair for pair in filtered_pairs if pair["verified"]])
        
        query_types = {}
        for pair in filtered_pairs:
            query_type = pair["query_type"]
            query_types[query_type] = query_types.get(query_type, 0) + 1
        
        return {
            "total_qa_pairs": total_count,
            "verified_qa_pairs": verified_count,
            "query_types": query_types,
            "average_success_rate": 0.0  # 简化版暂不计算
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.delete("/simple-qa-pairs/{qa_id}", response_model=Dict[str, str])
async def delete_simple_qa_pair(
    qa_id: str,
    db: Session = Depends(deps.get_db)
):
    """删除简化版问答对"""
    try:
        global qa_pairs_storage
        original_count = len(qa_pairs_storage)
        qa_pairs_storage = [pair for pair in qa_pairs_storage if pair["id"] != qa_id]
        
        if len(qa_pairs_storage) < original_count:
            return {"status": "success", "message": "问答对删除成功"}
        else:
            raise HTTPException(status_code=404, detail="问答对不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除问答对失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除问答对失败: {str(e)}")

@router.get("/simple-qa-pairs/health", response_model=Dict[str, Any])
async def simple_qa_health_check():
    """简化版问答对服务健康检查"""
    return {
        "status": "healthy",
        "service": "simple_qa",
        "timestamp": datetime.now().isoformat(),
        "total_pairs": len(qa_pairs_storage)
    }
