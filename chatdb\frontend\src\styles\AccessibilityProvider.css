/* 无障碍访问样式 */

/* 跳转到主内容链接 */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000000;
  color: white;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  z-index: 10000;
  transition: top 0.3s ease;
}

.skip-to-content:focus {
  top: 6px;
  color: white;
}

/* 无障碍设置面板 */
.accessibility-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 320px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 16px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #f0f0f0;
}

.accessibility-panel__content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.accessibility-panel__header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.accessibility-panel__header .ant-typography {
  color: white;
  margin: 0;
}

.accessibility-panel__header .ant-btn {
  color: white;
  border: none;
  background: transparent;
  font-size: 20px;
  padding: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.accessibility-panel__header .ant-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.accessibility-panel__body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.accessibility-panel__footer {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 设置项样式 */
.accessibility-setting {
  margin-bottom: 20px;
}

.accessibility-setting:last-child {
  margin-bottom: 0;
}

.accessibility-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.accessibility-switch .ant-typography {
  margin: 0;
  flex: 1;
}

/* 键盘导航增强 */
.keyboard-navigation *:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.keyboard-navigation-enabled *:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
  border-radius: 4px;
}

/* 屏幕阅读器专用样式 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 高对比度模式 */
.high-contrast,
[data-theme="high-contrast"] {
  --text-primary: #000000;
  --text-secondary: #000000;
  --text-tertiary: #000000;
  --text-inverse: #ffffff;
  --bg-primary: #ffffff;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f0f0f0;
  --border-primary: #000000;
  --border-secondary: #000000;
  --border-focus: #0066cc;
  --primary-500: #0066cc;
  --error-500: #cc0000;
  --success-500: #006600;
  --warning-500: #cc6600;
}

.high-contrast * {
  border-color: #000000 !important;
}

.high-contrast .ant-btn-primary {
  background: #0066cc !important;
  border-color: #0066cc !important;
  color: white !important;
}

.high-contrast .ant-btn-default {
  background: white !important;
  border-color: #000000 !important;
  color: #000000 !important;
}

/* 色盲友好模式 */
.color-blind-friendly {
  --primary-500: #0173b2;
  --success-500: #029e73;
  --warning-500: #cc78bc;
  --error-500: #de8f05;
}

.color-blind-friendly .ant-tag {
  border: 1px solid currentColor;
  font-weight: 500;
}

/* 减少动画模式 */
.reduced-motion,
.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* 字体大小调整 */
html[style*="font-size: 18px"] {
  --font-size-xs: 14px;
  --font-size-sm: 16px;
  --font-size-base: 18px;
  --font-size-lg: 20px;
  --font-size-xl: 22px;
  --font-size-2xl: 26px;
}

html[style*="font-size: 20px"] {
  --font-size-xs: 16px;
  --font-size-sm: 18px;
  --font-size-base: 20px;
  --font-size-lg: 22px;
  --font-size-xl: 24px;
  --font-size-2xl: 28px;
}

html[style*="font-size: 22px"] {
  --font-size-xs: 18px;
  --font-size-sm: 20px;
  --font-size-base: 22px;
  --font-size-lg: 24px;
  --font-size-xl: 26px;
  --font-size-2xl: 30px;
}

html[style*="font-size: 24px"] {
  --font-size-xs: 20px;
  --font-size-sm: 22px;
  --font-size-base: 24px;
  --font-size-lg: 26px;
  --font-size-xl: 28px;
  --font-size-2xl: 32px;
}

/* 暗色主题下的无障碍面板 */
[data-theme="dark"] .accessibility-panel {
  background: #1f1f1f;
  border-left-color: #434343;
}

[data-theme="dark"] .accessibility-panel__header {
  border-bottom-color: #434343;
}

[data-theme="dark"] .accessibility-panel__body {
  color: white;
}

[data-theme="dark"] .accessibility-panel__footer {
  border-top-color: #434343;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .accessibility-panel {
    width: 100%;
    right: 0;
  }
  
  .accessibility-panel__header {
    padding: 16px;
  }
  
  .accessibility-panel__body {
    padding: 16px;
  }
  
  .accessibility-panel__footer {
    padding: 16px;
    flex-direction: column;
  }
  
  .accessibility-panel__footer .ant-btn {
    width: 100%;
  }
}

/* 打印样式 */
@media print {
  .accessibility-panel,
  .skip-to-content {
    display: none;
  }
}
