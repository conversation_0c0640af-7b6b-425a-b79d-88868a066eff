import React, { useState, useEffect } from 'react';
import { Card, Space, Divider, Select, message } from 'antd';
import { SendOutlined, DatabaseOutlined, SettingOutlined, HistoryOutlined } from '@ant-design/icons';

// 导入优化组件
import ResponsiveLayout from './ResponsiveLayout';
import EnhancedButton from './EnhancedButton';
import EnhancedInput from './EnhancedInput';
import ErrorBoundary from './ErrorBoundary';
import { SmartLoading, SkeletonLoading } from './LoadingStates';
import { AccessibilityProvider, AccessibilityPanel, SkipToContent } from './AccessibilityProvider';

// 导入样式
import '../styles/DesignSystem.css';
import '../styles/ResponsiveLayout.css';
import '../styles/EnhancedButton.css';
import '../styles/EnhancedInput.css';
import '../styles/LoadingStates.css';
import '../styles/ErrorBoundary.css';
import '../styles/AccessibilityProvider.css';

const { Option } = Select;

interface Connection {
  id: number;
  name: string;
  type: string;
  status: 'connected' | 'disconnected';
}

const UXOptimizedExample: React.FC = () => {
  const [query, setQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAccessibilityPanel, setShowAccessibilityPanel] = useState(false);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [selectedConnection, setSelectedConnection] = useState<number | null>(null);
  const [queryHistory, setQueryHistory] = useState<string[]>([]);

  // 模拟数据加载
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setConnections([
        { id: 1, name: '财务数据库', type: 'MySQL', status: 'connected' },
        { id: 2, name: '用户数据库', type: 'PostgreSQL', status: 'connected' },
        { id: 3, name: '日志数据库', type: 'MongoDB', status: 'disconnected' }
      ]);
      setQueryHistory([
        '查询上个月的销售数据',
        '统计用户注册趋势',
        '分析产品销售排行'
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleSubmit = async () => {
    if (!query.trim()) {
      message.warning('请输入查询内容');
      return;
    }
    
    if (!selectedConnection) {
      message.warning('请选择数据库连接');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 添加到历史记录
      setQueryHistory(prev => [query, ...prev.slice(0, 9)]);
      message.success('查询执行成功！');
      
      console.log('Query submitted:', { query, connection: selectedConnection });
    } catch (err) {
      setError('查询失败，请检查网络连接或重试');
      message.error('查询执行失败');
    } finally {
      setLoading(false);
    }
  };

  const handleError = (error: Error) => {
    console.error('Component error:', error);
    setError(error.message);
    message.error('组件发生错误');
  };

  const handleHistoryClick = (historyQuery: string) => {
    setQuery(historyQuery);
  };

  // 侧边栏内容
  const sidebarContent = (
    <div className="text2sql-sidebar" style={{ padding: '16px' }}>
      <div className="sidebar-section">
        <h3 style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <HistoryOutlined />
          查询历史
        </h3>
        <SmartLoading
          loading={loading}
          skeleton={<SkeletonLoading type="list" rows={3} />}
        >
          <div className="history-list">
            {queryHistory.map((item, index) => (
              <div 
                key={index}
                className="history-item"
                style={{
                  padding: '8px 12px',
                  margin: '4px 0',
                  background: '#f5f5f5',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  fontSize: '13px'
                }}
                onClick={() => handleHistoryClick(item)}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = '#e6f7ff';
                  e.currentTarget.style.transform = 'translateX(2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = '#f5f5f5';
                  e.currentTarget.style.transform = 'translateX(0)';
                }}
              >
                {item}
              </div>
            ))}
          </div>
        </SmartLoading>
      </div>
      
      <Divider />
      
      <div className="sidebar-section">
        <h3 style={{ marginBottom: '16px' }}>💡 智能建议</h3>
        <div className="suggestions">
          <div className="suggestion-item" style={{ 
            padding: '12px', 
            background: '#f0f9ff', 
            borderRadius: '8px', 
            marginBottom: '8px',
            fontSize: '13px',
            lineHeight: '1.4'
          }}>
            <span>💡 尝试使用自然语言描述您的查询需求</span>
          </div>
          <div className="suggestion-item" style={{ 
            padding: '12px', 
            background: '#f0f9ff', 
            borderRadius: '8px', 
            marginBottom: '8px',
            fontSize: '13px',
            lineHeight: '1.4'
          }}>
            <span>📊 可以要求生成图表和可视化</span>
          </div>
          <div className="suggestion-item" style={{ 
            padding: '12px', 
            background: '#f0f9ff', 
            borderRadius: '8px',
            fontSize: '13px',
            lineHeight: '1.4'
          }}>
            <span>⚡ 支持复杂的多表关联查询</span>
          </div>
        </div>
      </div>
    </div>
  );

  // 头部内容
  const headerContent = (
    <div className="text2sql-header" style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'space-between',
      width: '100%'
    }}>
      <div className="header-title" style={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: '12px',
        color: 'white',
        fontSize: '18px',
        fontWeight: '600'
      }}>
        <DatabaseOutlined style={{ fontSize: '20px' }} />
        <span>智能SQL生成器</span>
      </div>
      
      <div className="header-actions">
        <EnhancedButton
          variant="ghost"
          size="small"
          icon={<SettingOutlined />}
          onClick={() => setShowAccessibilityPanel(true)}
          tooltip="无障碍设置"
          style={{ color: 'white', borderColor: 'rgba(255,255,255,0.3)' }}
        >
          设置
        </EnhancedButton>
      </div>
    </div>
  );

  return (
    <AccessibilityProvider>
      <ErrorBoundary onError={handleError} level="page">
        <SkipToContent />
        
        <ResponsiveLayout
          sidebar={sidebarContent}
          header={headerContent}
          showSidebar={true}
          collapsible={true}
        >
          <main id="main-content" className="text2sql-main" style={{ padding: '24px' }}>
            <div className="text2sql-content">
              {error && (
                <Card className="error-card" style={{ marginBottom: '16px' }}>
                  <div className="error-message" style={{ 
                    color: '#ff4d4f', 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: '8px' 
                  }}>
                    ⚠️ {error}
                  </div>
                </Card>
              )}

              <Card className="query-card" style={{ marginBottom: '16px' }}>
                <div className="query-section">
                  <div style={{ marginBottom: '16px' }}>
                    <label style={{ 
                      display: 'block', 
                      marginBottom: '8px', 
                      fontWeight: '500',
                      color: '#262626'
                    }}>
                      数据库连接 <span style={{ color: '#ff4d4f' }}>*</span>
                    </label>
                    <Select
                      placeholder="选择数据库连接"
                      value={selectedConnection}
                      onChange={setSelectedConnection}
                      style={{ width: '100%' }}
                      size="large"
                    >
                      {connections.map(conn => (
                        <Option key={conn.id} value={conn.id} disabled={conn.status === 'disconnected'}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span style={{ 
                              width: '8px', 
                              height: '8px', 
                              borderRadius: '50%',
                              background: conn.status === 'connected' ? '#52c41a' : '#ff4d4f'
                            }} />
                            {conn.name} ({conn.type})
                          </div>
                        </Option>
                      ))}
                    </Select>
                  </div>

                  <EnhancedInput
                    label="查询描述"
                    placeholder="用自然语言描述您想要查询的内容..."
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    autoResize
                    maxLength={500}
                    showCount
                    variant="outlined"
                    size="large"
                    helperText="例如：查询上个月销售额最高的前10个产品"
                    required
                  />

                  <div className="query-actions" style={{ 
                    display: 'flex', 
                    justifyContent: 'flex-end', 
                    marginTop: '16px' 
                  }}>
                    <Space>
                      <EnhancedButton
                        variant="secondary"
                        onClick={() => setQuery('')}
                        disabled={!query || loading}
                      >
                        清空
                      </EnhancedButton>
                      
                      <EnhancedButton
                        variant="primary"
                        icon={<SendOutlined />}
                        onClick={handleSubmit}
                        loading={loading}
                        loadingText="生成中..."
                        disabled={!query.trim() || !selectedConnection}
                        elevated
                        tooltip="生成SQL查询"
                      >
                        生成查询
                      </EnhancedButton>
                    </Space>
                  </div>
                </div>
              </Card>

              <SmartLoading
                loading={loading}
                skeleton={<SkeletonLoading type="card" rows={4} />}
              >
                <Card className="results-card">
                  <div className="results-content">
                    <h3 style={{ marginBottom: '16px' }}>查询结果</h3>
                    <p style={{ color: '#8c8c8c' }}>
                      这里将显示生成的SQL查询和执行结果。支持语法高亮、结果导出和可视化图表。
                    </p>
                  </div>
                </Card>
              </SmartLoading>
            </div>
          </main>
        </ResponsiveLayout>

        <AccessibilityPanel
          visible={showAccessibilityPanel}
          onClose={() => setShowAccessibilityPanel(false)}
        />
      </ErrorBoundary>
    </AccessibilityProvider>
  );
};

export default UXOptimizedExample;
