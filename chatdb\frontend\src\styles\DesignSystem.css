/* 设计系统 - 统一的颜色、字体和间距规范 */

:root {
  /* 主色调 */
  --primary-50: #e6f7ff;
  --primary-100: #bae7ff;
  --primary-200: #91d5ff;
  --primary-300: #69c0ff;
  --primary-400: #40a9ff;
  --primary-500: #1890ff;
  --primary-600: #096dd9;
  --primary-700: #0050b3;
  --primary-800: #003a8c;
  --primary-900: #002766;

  /* 辅助色调 */
  --success-50: #f6ffed;
  --success-100: #d9f7be;
  --success-200: #b7eb8f;
  --success-300: #95de64;
  --success-400: #73d13d;
  --success-500: #52c41a;
  --success-600: #389e0d;
  --success-700: #237804;
  --success-800: #135200;
  --success-900: #092b00;

  --warning-50: #fffbe6;
  --warning-100: #fff1b8;
  --warning-200: #ffe58f;
  --warning-300: #ffd666;
  --warning-400: #ffc53d;
  --warning-500: #faad14;
  --warning-600: #d48806;
  --warning-700: #ad6800;
  --warning-800: #874d00;
  --warning-900: #613400;

  --error-50: #fff2f0;
  --error-100: #ffccc7;
  --error-200: #ffa39e;
  --error-300: #ff7875;
  --error-400: #ff4d4f;
  --error-500: #f5222d;
  --error-600: #cf1322;
  --error-700: #a8071a;
  --error-800: #820014;
  --error-900: #5c0011;

  /* 中性色调 */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #f0f0f0;
  --gray-300: #d9d9d9;
  --gray-400: #bfbfbf;
  --gray-500: #8c8c8c;
  --gray-600: #595959;
  --gray-700: #434343;
  --gray-800: #262626;
  --gray-900: #1f1f1f;

  /* 语义化颜色 */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-tertiary: var(--gray-500);
  --text-disabled: var(--gray-400);
  --text-inverse: #ffffff;

  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-disabled: var(--gray-200);
  --bg-overlay: rgba(0, 0, 0, 0.45);

  --border-primary: var(--gray-300);
  --border-secondary: var(--gray-200);
  --border-focus: var(--primary-500);
  --border-error: var(--error-500);
  --border-success: var(--success-500);

  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

  /* 字体 */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 间距 */
  --spacing-0: 0;
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-8: 32px;
  --spacing-10: 40px;
  --spacing-12: 48px;
  --spacing-16: 64px;
  --spacing-20: 80px;
  --spacing-24: 96px;

  /* 圆角 */
  --radius-none: 0;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-full: 9999px;

  /* 过渡动画 */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* 暗色主题 */
[data-theme="dark"] {
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.85);
  --text-tertiary: rgba(255, 255, 255, 0.65);
  --text-disabled: rgba(255, 255, 255, 0.45);
  --text-inverse: var(--gray-900);

  --bg-primary: #141414;
  --bg-secondary: #1f1f1f;
  --bg-tertiary: #262626;
  --bg-disabled: #434343;
  --bg-overlay: rgba(0, 0, 0, 0.65);

  --border-primary: #434343;
  --border-secondary: #303030;
  --border-focus: var(--primary-400);
  --border-error: var(--error-400);
  --border-success: var(--success-400);
}

/* 高对比度主题 */
[data-theme="high-contrast"] {
  --text-primary: #000000;
  --text-secondary: #000000;
  --text-tertiary: #000000;
  --text-inverse: #ffffff;

  --bg-primary: #ffffff;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f0f0f0;

  --border-primary: #000000;
  --border-secondary: #000000;
  --border-focus: #0066cc;
  --border-error: #cc0000;
  --border-success: #006600;

  --primary-500: #0066cc;
  --error-500: #cc0000;
  --success-500: #006600;
  --warning-500: #cc6600;
}

/* 工具类 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-disabled { color: var(--text-disabled); }
.text-inverse { color: var(--text-inverse); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.border-primary { border-color: var(--border-primary); }
.border-secondary { border-color: var(--border-secondary); }
.border-focus { border-color: var(--border-focus); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.transition-fast { transition: all var(--transition-fast); }
.transition-normal { transition: all var(--transition-normal); }
.transition-slow { transition: all var(--transition-slow); }

/* 响应式断点 */
@media (max-width: 640px) {
  :root {
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-size-2xl: 20px;
    --font-size-3xl: 24px;
    --font-size-4xl: 28px;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: none;
    --transition-normal: none;
    --transition-slow: none;
  }
  
  .transition-fast,
  .transition-normal,
  .transition-slow {
    transition: none;
  }
}
