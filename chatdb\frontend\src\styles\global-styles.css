/* 全局样式 - 优化版本 */
html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-primary, #1f2937);
  background-color: var(--bg-primary, #ffffff);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: 'kern' 1;
  font-kerning: normal;
  scroll-behavior: smooth;
}

/* 暗色模式 */
@media (prefers-color-scheme: dark) {
  body {
    color: var(--text-primary, #e5e7eb);
    background-color: var(--bg-primary, #111827);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  body {
    color: #000000;
    background-color: #ffffff;
  }
}

/* 字体大小偏好 */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}
