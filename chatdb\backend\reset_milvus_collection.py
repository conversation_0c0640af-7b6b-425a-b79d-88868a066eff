#!/usr/bin/env python3
"""
重置Milvus集合以修复字段结构问题
"""

import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pymilvus import connections, Collection, utility, FieldSchema, CollectionSchema, DataType
from app.core.config import settings

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def reset_milvus_collection():
    """重置Milvus集合"""
    
    logger.info("🔄 开始重置Milvus集合...")
    
    try:
        # 连接到Milvus
        connections.connect("default", host=settings.MILVUS_HOST, port=settings.MILVUS_PORT)
        logger.info(f"✅ 连接到Milvus: {settings.MILVUS_HOST}:{settings.MILVUS_PORT}")
        
        collection_name = "qa_pairs"
        
        # 检查集合是否存在
        if utility.has_collection(collection_name):
            logger.info(f"📋 找到现有集合: {collection_name}")
            
            # 获取现有数据（如果需要备份）
            existing_collection = Collection(collection_name)
            existing_collection.load()
            
            try:
                # 尝试获取现有数据进行备份
                results = existing_collection.query(
                    expr="",
                    output_fields=["id", "question", "sql", "connection_id", "difficulty_level", "query_type", "success_rate", "verified"],
                    limit=1000
                )
                logger.info(f"📊 备份了 {len(results)} 条现有记录")
                
                # 保存备份数据
                backup_data = results
                
            except Exception as e:
                logger.warning(f"⚠️ 无法备份现有数据: {str(e)}")
                backup_data = []
            
            # 删除现有集合
            utility.drop_collection(collection_name)
            logger.info(f"🗑️ 删除了现有集合: {collection_name}")
        else:
            logger.info(f"📋 集合不存在: {collection_name}")
            backup_data = []
        
        # 创建新的集合schema（包含created_at字段）
        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=100, is_primary=True),
            FieldSchema(name="question", dtype=DataType.VARCHAR, max_length=2000),
            FieldSchema(name="sql", dtype=DataType.VARCHAR, max_length=5000),
            FieldSchema(name="connection_id", dtype=DataType.INT64),
            FieldSchema(name="difficulty_level", dtype=DataType.INT64),
            FieldSchema(name="query_type", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="success_rate", dtype=DataType.FLOAT),
            FieldSchema(name="verified", dtype=DataType.BOOL),
            FieldSchema(name="created_at", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=768)  # BGE向量维度
        ]
        
        schema = CollectionSchema(fields, "QA pairs for Text2SQL optimization with created_at field")
        
        # 创建新集合
        new_collection = Collection(collection_name, schema)
        logger.info(f"✅ 创建了新集合: {collection_name}")
        
        # 创建索引
        index_params = {
            "metric_type": "COSINE",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }
        new_collection.create_index("embedding", index_params)
        logger.info("📈 创建了向量索引")
        
        # 加载集合
        new_collection.load()
        logger.info("💾 集合已加载到内存")
        
        # 如果有备份数据，尝试恢复（需要重新生成向量）
        if backup_data:
            logger.info(f"🔄 尝试恢复 {len(backup_data)} 条备份数据...")
            logger.warning("⚠️ 注意：由于需要重新生成向量，备份数据恢复需要BGE服务支持")
            logger.info("💡 建议：重新创建问答对以确保数据完整性")
        
        logger.info("🎉 Milvus集合重置完成!")
        logger.info("💡 现在可以重新启动后端服务并创建问答对了")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 重置Milvus集合失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False

def main():
    logger.info("🚀 开始重置Milvus集合...")
    
    success = reset_milvus_collection()
    
    if success:
        logger.info("✅ Milvus集合重置成功!")
        logger.info("📝 下一步:")
        logger.info("   1. 重新启动后端服务")
        logger.info("   2. 在前端重新创建问答对")
        logger.info("   3. 验证数据是否正确显示")
    else:
        logger.error("❌ Milvus集合重置失败!")

if __name__ == "__main__":
    main()
