import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Collapse, Alert } from 'antd';
import { 
  BugOutlined, 
  ReloadOutlined, 
  HomeOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import '../styles/ErrorBoundary.css';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 调用外部错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 发送错误报告到监控服务
    this.reportError(error, errorInfo);
  }

  reportError = (error: Error, errorInfo: ErrorInfo) => {
    // 这里可以集成错误监控服务，如 Sentry
    console.error('Error Boundary caught an error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  render() {
    if (this.state.hasError) {
      const { fallback, showDetails = true, level = 'component' } = this.props;
      const { error, errorInfo, errorId } = this.state;

      // 如果提供了自定义fallback，使用它
      if (fallback) {
        return fallback;
      }

      // 页面级错误
      if (level === 'page') {
        return (
          <div className="error-boundary error-boundary--page">
            <Result
              status="500"
              title="页面出现错误"
              subTitle="抱歉，页面遇到了一些问题。请尝试刷新页面或联系技术支持。"
              icon={<BugOutlined />}
              extra={[
                <Button type="primary" icon={<ReloadOutlined />} onClick={this.handleReload} key="reload">
                  刷新页面
                </Button>,
                <Button icon={<HomeOutlined />} onClick={this.handleGoHome} key="home">
                  返回首页
                </Button>
              ]}
            >
              {showDetails && error && (
                <div className="error-details">
                  <Alert
                    message="错误详情"
                    description={
                      <div>
                        <Paragraph>
                          <Text strong>错误ID:</Text> {errorId}
                        </Paragraph>
                        <Paragraph>
                          <Text strong>错误信息:</Text> {error.message}
                        </Paragraph>
                        <Collapse ghost>
                          <Panel 
                            header={
                              <span>
                                <InfoCircleOutlined /> 技术详情
                              </span>
                            } 
                            key="1"
                          >
                            <div className="error-stack">
                              <Text code>{error.stack}</Text>
                            </div>
                            {errorInfo && (
                              <div className="error-component-stack">
                                <Text strong>组件堆栈:</Text>
                                <Text code>{errorInfo.componentStack}</Text>
                              </div>
                            )}
                          </Panel>
                        </Collapse>
                      </div>
                    }
                    type="error"
                    showIcon
                  />
                </div>
              )}
            </Result>
          </div>
        );
      }

      // 组件级错误
      return (
        <div className="error-boundary error-boundary--component">
          <Alert
            message="组件加载失败"
            description={
              <div>
                <Paragraph>
                  该组件遇到了错误，请尝试重新加载。
                </Paragraph>
                {showDetails && (
                  <Paragraph>
                    <Text type="secondary">错误ID: {errorId}</Text>
                  </Paragraph>
                )}
              </div>
            }
            type="error"
            showIcon
            icon={<ExclamationCircleOutlined />}
            action={
              <Button size="small" onClick={this.handleRetry}>
                重试
              </Button>
            }
          />
          
          {showDetails && error && (
            <Collapse ghost className="error-details-collapse">
              <Panel header="查看详细信息" key="1">
                <div className="error-details">
                  <Paragraph>
                    <Text strong>错误信息:</Text> {error.message}
                  </Paragraph>
                  <div className="error-stack">
                    <Text code>{error.stack}</Text>
                  </div>
                </div>
              </Panel>
            </Collapse>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

// 函数式错误边界Hook
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = () => setError(null);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { handleError, resetError };
};

// 异步错误处理组件
export const AsyncErrorBoundary: React.FC<{
  children: ReactNode;
  onError?: (error: Error) => void;
}> = ({ children, onError }) => {
  const { handleError } = useErrorHandler();

  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = new Error(event.reason);
      handleError(error);
      if (onError) onError(error);
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [handleError, onError]);

  return <>{children}</>;
};

export default ErrorBoundary;
