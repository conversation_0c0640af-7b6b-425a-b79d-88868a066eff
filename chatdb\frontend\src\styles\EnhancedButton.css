/* 增强按钮样式 */

.enhanced-button {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
  outline: none;
  border: none;
}

/* 尺寸变体 */
.enhanced-button--small {
  padding: 6px 12px;
  font-size: 12px;
  min-height: 32px;
  border-radius: 6px;
}

.enhanced-button--medium {
  padding: 8px 16px;
  font-size: 14px;
  min-height: 40px;
  border-radius: 8px;
}

.enhanced-button--large {
  padding: 12px 24px;
  font-size: 16px;
  min-height: 48px;
  border-radius: 10px;
}

/* 颜色变体 */
.enhanced-button--primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.enhanced-button--primary:hover:not(.enhanced-button--loading):not(:disabled) {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

.enhanced-button--secondary {
  background: linear-gradient(135deg, #f0f0f0 0%, #e6e6e6 100%);
  color: #595959;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enhanced-button--secondary:hover:not(.enhanced-button--loading):not(:disabled) {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.enhanced-button--success {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.enhanced-button--success:hover:not(.enhanced-button--loading):not(:disabled) {
  background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
  transform: translateY(-1px);
}

.enhanced-button--warning {
  background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(250, 173, 20, 0.3);
}

.enhanced-button--warning:hover:not(.enhanced-button--loading):not(:disabled) {
  background: linear-gradient(135deg, #ffc53d 0%, #faad14 100%);
  box-shadow: 0 4px 12px rgba(250, 173, 20, 0.4);
  transform: translateY(-1px);
}

.enhanced-button--danger {
  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
}

.enhanced-button--danger:hover:not(.enhanced-button--loading):not(:disabled) {
  background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);
  transform: translateY(-1px);
}

.enhanced-button--ghost {
  background: transparent;
  color: #1890ff;
  border: 1px solid #1890ff;
  box-shadow: none;
}

.enhanced-button--ghost:hover:not(.enhanced-button--loading):not(:disabled) {
  background: rgba(24, 144, 255, 0.1);
  border-color: #40a9ff;
  color: #40a9ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 特殊效果 */
.enhanced-button--elevated {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.enhanced-button--elevated:hover:not(.enhanced-button--loading):not(:disabled) {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.enhanced-button--full-width {
  width: 100%;
}

/* 加载状态 */
.enhanced-button--loading {
  cursor: not-allowed;
  opacity: 0.8;
}

.enhanced-button__loading-icon {
  animation: spin 1s linear infinite;
}

.enhanced-button__content {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 禁用状态 */
.enhanced-button:disabled {
  background: #f5f5f5 !important;
  color: #bfbfbf !important;
  border-color: #d9d9d9 !important;
  box-shadow: none !important;
  cursor: not-allowed;
  transform: none !important;
}

/* 波纹效果 */
.enhanced-button--ripple {
  position: relative;
  overflow: hidden;
}

.button-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 焦点状态 */
.enhanced-button:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .enhanced-button--primary {
    background: #0050b3;
    border: 2px solid #ffffff;
  }
  
  .enhanced-button--secondary {
    background: #ffffff;
    color: #000000;
    border: 2px solid #000000;
  }
  
  .enhanced-button--ghost {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .enhanced-button {
    transition: none;
  }
  
  .enhanced-button:hover {
    transform: none;
  }
  
  .enhanced-button__loading-icon {
    animation: none;
  }
  
  .button-ripple {
    animation: none;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .enhanced-button--small {
    min-height: 36px;
    padding: 8px 14px;
  }
  
  .enhanced-button--medium {
    min-height: 44px;
    padding: 10px 18px;
  }
  
  .enhanced-button--large {
    min-height: 52px;
    padding: 14px 26px;
  }
}
