{"ast": null, "code": "// 混合问答对服务\n\nimport axios from 'axios';\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\nexport const hybridQAService = {\n  // 获取问答对列表\n  async getQAPairs(connectionId, skip = 0, limit = 50) {\n    const params = {\n      skip,\n      limit\n    };\n    if (connectionId !== undefined) {\n      params.connection_id = connectionId;\n    }\n    const response = await api.get('/hybrid-qa/qa-pairs/', {\n      params\n    });\n    return response.data;\n  },\n  // 创建问答对\n  async createQAPair(data) {\n    const response = await api.post('/hybrid-qa/qa-pairs/', data);\n    return response.data;\n  },\n  // 批量创建问答对\n  async batchCreateQAPairs(data) {\n    const response = await api.post('/hybrid-qa/qa-pairs/batch-create', data);\n    return response.data;\n  },\n  // 搜索相似问答对\n  async searchSimilar(data) {\n    const response = await api.post('/hybrid-qa/qa-pairs/search', data);\n    return response.data;\n  },\n  // 获取统计信息\n  async getStats(connectionId) {\n    const params = connectionId ? {\n      connection_id: connectionId\n    } : {};\n    const response = await api.get('/hybrid-qa/qa-pairs/stats', {\n      params\n    });\n    return response.data;\n  },\n  // 提交反馈\n  async submitFeedback(data) {\n    const response = await api.post('/hybrid-qa/qa-pairs/feedback', data);\n    return response.data;\n  },\n  // 删除问答对\n  async deleteQAPair(qaId) {\n    const response = await api.delete(`/hybrid-qa/qa-pairs/${qaId}`);\n    return response.data;\n  },\n  // 导出问答对\n  async exportQAPairs(connectionId, format = 'json') {\n    const params = {\n      format,\n      ...(connectionId && {\n        connection_id: connectionId\n      })\n    };\n    const response = await api.get('/hybrid-qa/qa-pairs/export', {\n      params\n    });\n    return response.data;\n  },\n  // 健康检查\n  async healthCheck() {\n    const response = await api.get('/hybrid-qa/qa-pairs/health');\n    return response.data;\n  },\n  // 获取推荐示例（用于Text2SQL页面）\n  async getRecommendedExamples(question, connectionId, schemaContext, topK = 3) {\n    const response = await api.post('/hybrid-qa/qa-pairs/search', {\n      question,\n      connection_id: connectionId,\n      schema_context: schemaContext,\n      top_k: topK\n    });\n    return response.data;\n  }\n};\n\n// 混合检索增强的Text2SQL服务\nexport const enhancedText2SQLService = {\n  // 增强的SQL生成（集成混合检索）\n  async generateSQLWithExamples(data) {\n    // 首先获取相似示例\n    let similarExamples = [];\n    if (data.use_hybrid_retrieval !== false) {\n      try {\n        similarExamples = await hybridQAService.getRecommendedExamples(data.query, data.connection_id, data.schema_context, 3);\n      } catch (error) {\n        console.warn('获取相似示例失败，使用标准模式:', error);\n      }\n    }\n\n    // 调用增强的Text2SQL API\n    const response = await api.post('/text2sql/generate-enhanced', {\n      ...data,\n      similar_examples: similarExamples\n    });\n    return {\n      ...response.data,\n      similar_examples: similarExamples\n    };\n  },\n  // 学习用户确认的问答对\n  async learnFromUserConfirmation(data) {\n    const response = await api.post('/hybrid-qa/qa-pairs/learn', data);\n    return response.data;\n  }\n};", "map": {"version": 3, "names": ["axios", "API_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "hybridQAService", "getQAPairs", "connectionId", "skip", "limit", "params", "undefined", "connection_id", "response", "get", "data", "createQAPair", "post", "batchCreateQAPairs", "searchSimilar", "getStats", "submitFeedback", "deleteQAPair", "qaId", "delete", "exportQAPairs", "format", "healthCheck", "getRecommendedExamples", "question", "schemaContext", "topK", "schema_context", "top_k", "enhancedText2SQLService", "generateSQLWithExamples", "similarExamples", "use_hybrid_retrieval", "query", "error", "console", "warn", "similar_examples", "learnFromUserConfirmation"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/services/hybridQA.ts"], "sourcesContent": ["// 混合问答对服务\n\nimport axios from 'axios';\nimport type { SimilarQAPair, QAPairCreate, SearchRequest, FeedbackRequest } from '../types/hybridQA';\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\nexport const hybridQAService = {\n  // 获取问答对列表\n  async getQAPairs(connectionId?: number, skip: number = 0, limit: number = 50): Promise<any[]> {\n    const params: any = { skip, limit };\n    if (connectionId !== undefined) {\n      params.connection_id = connectionId;\n    }\n    const response = await api.get('/hybrid-qa/qa-pairs/', { params });\n    return response.data;\n  },\n\n  // 创建问答对\n  async createQAPair(data: QAPairCreate): Promise<{ status: string; qa_id: string; message: string }> {\n    const response = await api.post('/hybrid-qa/qa-pairs/', data);\n    return response.data;\n  },\n\n  // 批量创建问答对\n  async batchCreateQAPairs(data: QAPairCreate[]): Promise<{\n    status: string;\n    created_count: number;\n    failed_count: number;\n    errors: string[];\n  }> {\n    const response = await api.post('/hybrid-qa/qa-pairs/batch-create', data);\n    return response.data;\n  },\n\n  // 搜索相似问答对\n  async searchSimilar(data: SearchRequest): Promise<SimilarQAPair[]> {\n    const response = await api.post('/hybrid-qa/qa-pairs/search', data);\n    return response.data;\n  },\n\n  // 获取统计信息\n  async getStats(connectionId?: number): Promise<{\n    total_qa_pairs: number;\n    verified_qa_pairs: number;\n    query_types: Record<string, number>;\n    difficulty_distribution: Record<string, number>;\n    average_success_rate: number;\n  }> {\n    const params = connectionId ? { connection_id: connectionId } : {};\n    const response = await api.get('/hybrid-qa/qa-pairs/stats', { params });\n    return response.data;\n  },\n\n  // 提交反馈\n  async submitFeedback(data: FeedbackRequest): Promise<{ status: string; message: string }> {\n    const response = await api.post('/hybrid-qa/qa-pairs/feedback', data);\n    return response.data;\n  },\n\n  // 删除问答对\n  async deleteQAPair(qaId: string): Promise<{ status: string; message: string }> {\n    const response = await api.delete(`/hybrid-qa/qa-pairs/${qaId}`);\n    return response.data;\n  },\n\n  // 导出问答对\n  async exportQAPairs(connectionId?: number, format: string = 'json'): Promise<{\n    status: string;\n    message: string;\n    format: string;\n    connection_id?: number;\n  }> {\n    const params = { format, ...(connectionId && { connection_id: connectionId }) };\n    const response = await api.get('/hybrid-qa/qa-pairs/export', { params });\n    return response.data;\n  },\n\n  // 健康检查\n  async healthCheck(): Promise<{\n    status: string;\n    services?: Record<string, string>;\n    error?: string;\n    timestamp: string;\n  }> {\n    const response = await api.get('/hybrid-qa/qa-pairs/health');\n    return response.data;\n  },\n\n  // 获取推荐示例（用于Text2SQL页面）\n  async getRecommendedExamples(\n    question: string,\n    connectionId?: number,\n    schemaContext?: any,\n    topK: number = 3\n  ): Promise<SimilarQAPair[]> {\n    const response = await api.post('/hybrid-qa/qa-pairs/search', {\n      question,\n      connection_id: connectionId,\n      schema_context: schemaContext,\n      top_k: topK\n    });\n    return response.data;\n  }\n};\n\n// 混合检索增强的Text2SQL服务\nexport const enhancedText2SQLService = {\n  // 增强的SQL生成（集成混合检索）\n  async generateSQLWithExamples(data: {\n    query: string;\n    connection_id: number;\n    schema_context?: any;\n    use_hybrid_retrieval?: boolean;\n  }): Promise<{\n    sql: string;\n    explanation: string;\n    examples_used: number;\n    confidence_score: number;\n    similar_examples?: SimilarQAPair[];\n  }> {\n    // 首先获取相似示例\n    let similarExamples: SimilarQAPair[] = [];\n    if (data.use_hybrid_retrieval !== false) {\n      try {\n        similarExamples = await hybridQAService.getRecommendedExamples(\n          data.query,\n          data.connection_id,\n          data.schema_context,\n          3\n        );\n      } catch (error) {\n        console.warn('获取相似示例失败，使用标准模式:', error);\n      }\n    }\n\n    // 调用增强的Text2SQL API\n    const response = await api.post('/text2sql/generate-enhanced', {\n      ...data,\n      similar_examples: similarExamples\n    });\n\n    return {\n      ...response.data,\n      similar_examples: similarExamples\n    };\n  },\n\n  // 学习用户确认的问答对\n  async learnFromUserConfirmation(data: {\n    question: string;\n    sql: string;\n    connection_id: number;\n    schema_context?: any;\n    user_satisfaction: number;\n  }): Promise<{ status: string; message: string }> {\n    const response = await api.post('/hybrid-qa/qa-pairs/learn', data);\n    return response.data;\n  }\n};\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AAGzB,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAE5E,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,OAAO;EAChBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,OAAO,MAAMC,eAAe,GAAG;EAC7B;EACA,MAAMC,UAAUA,CAACC,YAAqB,EAAEC,IAAY,GAAG,CAAC,EAAEC,KAAa,GAAG,EAAE,EAAkB;IAC5F,MAAMC,MAAW,GAAG;MAAEF,IAAI;MAAEC;IAAM,CAAC;IACnC,IAAIF,YAAY,KAAKI,SAAS,EAAE;MAC9BD,MAAM,CAACE,aAAa,GAAGL,YAAY;IACrC;IACA,MAAMM,QAAQ,GAAG,MAAMZ,GAAG,CAACa,GAAG,CAAC,sBAAsB,EAAE;MAAEJ;IAAO,CAAC,CAAC;IAClE,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMC,YAAYA,CAACD,IAAkB,EAA+D;IAClG,MAAMF,QAAQ,GAAG,MAAMZ,GAAG,CAACgB,IAAI,CAAC,sBAAsB,EAAEF,IAAI,CAAC;IAC7D,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMG,kBAAkBA,CAACH,IAAoB,EAK1C;IACD,MAAMF,QAAQ,GAAG,MAAMZ,GAAG,CAACgB,IAAI,CAAC,kCAAkC,EAAEF,IAAI,CAAC;IACzE,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMI,aAAaA,CAACJ,IAAmB,EAA4B;IACjE,MAAMF,QAAQ,GAAG,MAAMZ,GAAG,CAACgB,IAAI,CAAC,4BAA4B,EAAEF,IAAI,CAAC;IACnE,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMK,QAAQA,CAACb,YAAqB,EAMjC;IACD,MAAMG,MAAM,GAAGH,YAAY,GAAG;MAAEK,aAAa,EAAEL;IAAa,CAAC,GAAG,CAAC,CAAC;IAClE,MAAMM,QAAQ,GAAG,MAAMZ,GAAG,CAACa,GAAG,CAAC,2BAA2B,EAAE;MAAEJ;IAAO,CAAC,CAAC;IACvE,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMM,cAAcA,CAACN,IAAqB,EAAgD;IACxF,MAAMF,QAAQ,GAAG,MAAMZ,GAAG,CAACgB,IAAI,CAAC,8BAA8B,EAAEF,IAAI,CAAC;IACrE,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMO,YAAYA,CAACC,IAAY,EAAgD;IAC7E,MAAMV,QAAQ,GAAG,MAAMZ,GAAG,CAACuB,MAAM,CAAC,uBAAuBD,IAAI,EAAE,CAAC;IAChE,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMU,aAAaA,CAAClB,YAAqB,EAAEmB,MAAc,GAAG,MAAM,EAK/D;IACD,MAAMhB,MAAM,GAAG;MAAEgB,MAAM;MAAE,IAAInB,YAAY,IAAI;QAAEK,aAAa,EAAEL;MAAa,CAAC;IAAE,CAAC;IAC/E,MAAMM,QAAQ,GAAG,MAAMZ,GAAG,CAACa,GAAG,CAAC,4BAA4B,EAAE;MAAEJ;IAAO,CAAC,CAAC;IACxE,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMY,WAAWA,CAAA,EAKd;IACD,MAAMd,QAAQ,GAAG,MAAMZ,GAAG,CAACa,GAAG,CAAC,4BAA4B,CAAC;IAC5D,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMa,sBAAsBA,CAC1BC,QAAgB,EAChBtB,YAAqB,EACrBuB,aAAmB,EACnBC,IAAY,GAAG,CAAC,EACU;IAC1B,MAAMlB,QAAQ,GAAG,MAAMZ,GAAG,CAACgB,IAAI,CAAC,4BAA4B,EAAE;MAC5DY,QAAQ;MACRjB,aAAa,EAAEL,YAAY;MAC3ByB,cAAc,EAAEF,aAAa;MAC7BG,KAAK,EAAEF;IACT,CAAC,CAAC;IACF,OAAOlB,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMmB,uBAAuB,GAAG;EACrC;EACA,MAAMC,uBAAuBA,CAACpB,IAK7B,EAME;IACD;IACA,IAAIqB,eAAgC,GAAG,EAAE;IACzC,IAAIrB,IAAI,CAACsB,oBAAoB,KAAK,KAAK,EAAE;MACvC,IAAI;QACFD,eAAe,GAAG,MAAM/B,eAAe,CAACuB,sBAAsB,CAC5Db,IAAI,CAACuB,KAAK,EACVvB,IAAI,CAACH,aAAa,EAClBG,IAAI,CAACiB,cAAc,EACnB,CACF,CAAC;MACH,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,kBAAkB,EAAEF,KAAK,CAAC;MACzC;IACF;;IAEA;IACA,MAAM1B,QAAQ,GAAG,MAAMZ,GAAG,CAACgB,IAAI,CAAC,6BAA6B,EAAE;MAC7D,GAAGF,IAAI;MACP2B,gBAAgB,EAAEN;IACpB,CAAC,CAAC;IAEF,OAAO;MACL,GAAGvB,QAAQ,CAACE,IAAI;MAChB2B,gBAAgB,EAAEN;IACpB,CAAC;EACH,CAAC;EAED;EACA,MAAMO,yBAAyBA,CAAC5B,IAM/B,EAAgD;IAC/C,MAAMF,QAAQ,GAAG,MAAMZ,GAAG,CAACgB,IAAI,CAAC,2BAA2B,EAAEF,IAAI,CAAC;IAClE,OAAOF,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}