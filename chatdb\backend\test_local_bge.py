#!/usr/bin/env python3
"""
测试本地BGE服务连接
"""

import asyncio
import httpx
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_local_bge_service():
    """测试本地BGE服务"""
    bge_url = "http://localhost:8080"
    
    try:
        async with httpx.AsyncClient() as client:
            # 测试健康检查
            logger.info("测试健康检查...")
            try:
                response = await client.get(f"{bge_url}/health", timeout=5.0)
                logger.info(f"健康检查响应: {response.status_code}")
                if response.status_code == 200:
                    logger.info("✅ 健康检查通过")
                else:
                    logger.warning(f"⚠️ 健康检查返回状态码: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ 健康检查失败: {str(e)}")
                return False
            
            # 测试单个文本向量化
            logger.info("测试单个文本向量化...")
            try:
                test_text = "这是一个测试文本"
                response = await client.post(
                    f"{bge_url}/embed",
                    json={"text": test_text},
                    timeout=10.0
                )
                logger.info(f"向量化响应状态: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    embedding = result.get("embedding", [])
                    logger.info(f"✅ 向量化成功! 向量维度: {len(embedding)}")
                    logger.info(f"向量前5个值: {embedding[:5]}")
                else:
                    logger.error(f"❌ 向量化失败: {response.status_code} - {response.text}")
                    return False
            except Exception as e:
                logger.error(f"❌ 向量化请求失败: {str(e)}")
                return False
            
            # 测试批量向量化
            logger.info("测试批量向量化...")
            try:
                test_texts = ["第一个测试文本", "第二个测试文本", "第三个测试文本"]
                response = await client.post(
                    f"{bge_url}/batch_embed",
                    json={"texts": test_texts},
                    timeout=15.0
                )
                logger.info(f"批量向量化响应状态: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    embeddings = result.get("embeddings", [])
                    logger.info(f"✅ 批量向量化成功! 处理了 {len(embeddings)} 个文本")
                    if embeddings:
                        logger.info(f"第一个向量维度: {len(embeddings[0])}")
                else:
                    logger.error(f"❌ 批量向量化失败: {response.status_code} - {response.text}")
                    return False
            except Exception as e:
                logger.error(f"❌ 批量向量化请求失败: {str(e)}")
                return False
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 连接本地BGE服务失败: {str(e)}")
        return False

async def main():
    logger.info("开始测试本地BGE服务...")
    success = await test_local_bge_service()
    
    if success:
        logger.info("🎉 本地BGE服务测试全部通过!")
    else:
        logger.error("💥 本地BGE服务测试失败!")
        logger.info("请确保:")
        logger.info("1. BGE服务正在 localhost:8080 运行")
        logger.info("2. 服务支持 /health, /embed, /batch_embed 接口")
        logger.info("3. 网络连接正常")

if __name__ == "__main__":
    asyncio.run(main())
