/* 增强输入框样式 */

.enhanced-input-container {
  margin-bottom: 16px;
}

.enhanced-input-container--with-label {
  margin-bottom: 20px;
}

.enhanced-input-container--with-helper {
  margin-bottom: 24px;
}

/* 标签样式 */
.enhanced-input__label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.4;
}

.enhanced-input__required {
  color: var(--error-500);
  margin-left: 2px;
}

/* 输入框基础样式 */
.enhanced-input {
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
}

/* 尺寸变体 */
.enhanced-input--small {
  padding: 6px 12px;
  font-size: 12px;
  min-height: 32px;
}

.enhanced-input--medium {
  padding: 8px 12px;
  font-size: 14px;
  min-height: 40px;
}

.enhanced-input--large {
  padding: 12px 16px;
  font-size: 16px;
  min-height: 48px;
}

/* 样式变体 */
.enhanced-input--outlined {
  border: 1px solid var(--border-primary);
  background: var(--bg-primary);
}

.enhanced-input--outlined:hover:not(:disabled) {
  border-color: var(--primary-400);
}

.enhanced-input--outlined.enhanced-input--focused {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.enhanced-input--filled {
  border: 1px solid transparent;
  background: var(--bg-tertiary);
}

.enhanced-input--filled:hover:not(:disabled) {
  background: var(--bg-secondary);
}

.enhanced-input--filled.enhanced-input--focused {
  background: var(--bg-primary);
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.enhanced-input--standard {
  border: none;
  border-bottom: 1px solid var(--border-primary);
  border-radius: 0;
  background: transparent;
  padding-left: 0;
  padding-right: 0;
}

.enhanced-input--standard:hover:not(:disabled) {
  border-bottom-color: var(--primary-400);
}

.enhanced-input--standard.enhanced-input--focused {
  border-bottom-color: var(--border-focus);
  box-shadow: 0 1px 0 0 var(--border-focus);
}

/* 状态样式 */
.enhanced-input--error {
  border-color: var(--border-error) !important;
}

.enhanced-input--error.enhanced-input--focused {
  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2) !important;
}

.enhanced-input--success {
  border-color: var(--border-success) !important;
}

.enhanced-input--success.enhanced-input--focused {
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
}

.enhanced-input--disabled {
  background: var(--bg-disabled) !important;
  color: var(--text-disabled) !important;
  cursor: not-allowed;
}

/* 后缀元素 */
.enhanced-input__suffix {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.enhanced-input__clear-icon,
.enhanced-input__password-toggle {
  color: var(--text-tertiary);
  cursor: pointer;
  transition: color 0.2s ease;
  padding: 2px;
  border-radius: 2px;
}

.enhanced-input__clear-icon:hover,
.enhanced-input__password-toggle:hover {
  color: var(--text-secondary);
  background: rgba(0, 0, 0, 0.04);
}

.enhanced-input__count {
  font-size: 12px;
  color: var(--text-tertiary);
  white-space: nowrap;
}

/* 帮助文本 */
.enhanced-input__helper {
  margin-top: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.enhanced-input__error-text {
  color: var(--error-500);
}

.enhanced-input__helper-text {
  color: var(--text-tertiary);
}

/* 焦点状态 */
.enhanced-input:focus-visible {
  outline: none;
}

/* 占位符样式 */
.enhanced-input::placeholder {
  color: var(--text-tertiary);
  opacity: 1;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .enhanced-input--small {
    min-height: 36px;
    padding: 8px 12px;
  }
  
  .enhanced-input--medium {
    min-height: 44px;
    padding: 10px 14px;
  }
  
  .enhanced-input--large {
    min-height: 52px;
    padding: 14px 16px;
  }
  
  .enhanced-input__label {
    font-size: 15px;
  }
}

/* 暗色主题 */
[data-theme="dark"] .enhanced-input--outlined {
  border-color: var(--border-primary);
  background: var(--bg-primary);
  color: var(--text-primary);
}

[data-theme="dark"] .enhanced-input--filled {
  background: var(--bg-tertiary);
}

/* 高对比度模式 */
[data-theme="high-contrast"] .enhanced-input {
  border-width: 2px;
}

[data-theme="high-contrast"] .enhanced-input--focused {
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3);
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .enhanced-input {
    transition: none;
  }
}
