#!/usr/bin/env python3
"""
找出BGE服务的正确API格式
"""

import asyncio
import httpx
import logging
import json

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_bge_api_formats():
    """测试不同的BGE API格式"""
    bge_url = "http://localhost:8080"
    
    logger.info(f"🔍 测试BGE服务API格式: {bge_url}")
    
    # 测试不同的端点和格式组合
    test_cases = [
        # 端点1: /embed
        {
            "endpoint": "/embed",
            "method": "POST",
            "data": {"inputs": "测试文本"},
            "content_type": "application/json"
        },
        {
            "endpoint": "/embed",
            "method": "POST", 
            "data": {"text": "测试文本"},
            "content_type": "application/json"
        },
        {
            "endpoint": "/embed",
            "method": "POST",
            "data": "测试文本",
            "content_type": "text/plain"
        },
        # 端点2: /embeddings
        {
            "endpoint": "/embeddings",
            "method": "POST",
            "data": {"inputs": "测试文本"},
            "content_type": "application/json"
        },
        {
            "endpoint": "/embeddings",
            "method": "POST",
            "data": {"text": "测试文本"},
            "content_type": "application/json"
        },
        # 端点3: /v1/embeddings (OpenAI格式)
        {
            "endpoint": "/v1/embeddings",
            "method": "POST",
            "data": {"input": "测试文本", "model": "bge-base-zh-v1.5"},
            "content_type": "application/json"
        },
        # 端点4: / (根路径)
        {
            "endpoint": "/",
            "method": "POST",
            "data": {"inputs": "测试文本"},
            "content_type": "application/json"
        }
    ]
    
    successful_formats = []
    
    try:
        async with httpx.AsyncClient(timeout=15.0) as client:
            for i, test_case in enumerate(test_cases):
                logger.info(f"🧪 测试格式 {i+1}: {test_case['endpoint']} - {test_case['data']}")
                
                try:
                    headers = {"Content-Type": test_case["content_type"]}
                    
                    if test_case["content_type"] == "application/json":
                        response = await client.request(
                            test_case["method"],
                            f"{bge_url}{test_case['endpoint']}",
                            json=test_case["data"],
                            headers=headers
                        )
                    else:
                        response = await client.request(
                            test_case["method"],
                            f"{bge_url}{test_case['endpoint']}",
                            content=test_case["data"],
                            headers=headers
                        )
                    
                    logger.info(f"   状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            logger.info(f"   ✅ 成功! 响应类型: {type(result)}")
                            
                            if isinstance(result, list) and result:
                                logger.info(f"   📊 向量维度: {len(result[0]) if result[0] else 0}")
                                logger.info(f"   🔢 前3个值: {result[0][:3] if result[0] else []}")
                            elif isinstance(result, dict):
                                logger.info(f"   📋 响应键: {list(result.keys())}")
                                
                            successful_formats.append({
                                "endpoint": test_case["endpoint"],
                                "method": test_case["method"],
                                "data_format": test_case["data"],
                                "content_type": test_case["content_type"],
                                "response_type": type(result).__name__,
                                "response_sample": str(result)[:200] + "..." if len(str(result)) > 200 else str(result)
                            })
                            
                        except json.JSONDecodeError:
                            logger.info(f"   ✅ 成功但非JSON响应: {response.text[:100]}...")
                            
                    elif response.status_code == 502:
                        logger.error(f"   ❌ 502 Bad Gateway")
                        logger.error(f"   响应: {response.text}")
                    else:
                        logger.warning(f"   ⚠️ 状态码: {response.status_code}")
                        logger.warning(f"   响应: {response.text[:100]}...")
                        
                except Exception as e:
                    logger.error(f"   ❌ 异常: {str(e)}")
                
                logger.info("")  # 空行分隔
    
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {str(e)}")
        return []
    
    return successful_formats

async def main():
    logger.info("🚀 开始寻找正确的BGE API格式...")
    
    successful_formats = await test_bge_api_formats()
    
    if successful_formats:
        logger.info("🎉 找到可用的API格式:")
        for i, fmt in enumerate(successful_formats):
            logger.info(f"格式 {i+1}:")
            logger.info(f"  端点: {fmt['endpoint']}")
            logger.info(f"  方法: {fmt['method']}")
            logger.info(f"  数据格式: {fmt['data_format']}")
            logger.info(f"  内容类型: {fmt['content_type']}")
            logger.info(f"  响应类型: {fmt['response_type']}")
            logger.info(f"  响应示例: {fmt['response_sample']}")
            logger.info("")
            
        # 推荐最佳格式
        best_format = successful_formats[0]
        logger.info(f"🏆 推荐使用格式:")
        logger.info(f"  URL: http://localhost:8080{best_format['endpoint']}")
        logger.info(f"  请求体: {best_format['data_format']}")
        logger.info(f"  Content-Type: {best_format['content_type']}")
        
    else:
        logger.error("💥 没有找到可用的API格式!")
        logger.info("💡 请检查BGE服务的文档或日志")

if __name__ == "__main__":
    asyncio.run(main())
