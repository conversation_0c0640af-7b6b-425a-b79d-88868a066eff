import React from 'react';
import { Spin, Skeleton, Progress } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import '../styles/LoadingStates.css';

// 自定义加载图标
const customLoadingIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

// 页面级加载组件
export const PageLoading: React.FC<{ tip?: string }> = ({ tip = '加载中...' }) => (
  <div className="page-loading">
    <div className="page-loading__content">
      <Spin size="large" indicator={customLoadingIcon} />
      <p className="page-loading__tip">{tip}</p>
    </div>
  </div>
);

// 内容区域加载组件
export const ContentLoading: React.FC<{ 
  tip?: string; 
  height?: number | string;
  transparent?: boolean;
}> = ({ 
  tip = '加载中...', 
  height = 200,
  transparent = false 
}) => (
  <div 
    className={`content-loading ${transparent ? 'content-loading--transparent' : ''}`}
    style={{ height }}
  >
    <Spin size="large" tip={tip} indicator={customLoadingIcon} />
  </div>
);

// 骨架屏组件
export const SkeletonLoading: React.FC<{
  type?: 'card' | 'list' | 'table' | 'chat';
  rows?: number;
  avatar?: boolean;
}> = ({ 
  type = 'card', 
  rows = 3,
  avatar = false 
}) => {
  switch (type) {
    case 'card':
      return (
        <div className="skeleton-loading skeleton-loading--card">
          <Skeleton active avatar={avatar} paragraph={{ rows }} />
        </div>
      );
    
    case 'list':
      return (
        <div className="skeleton-loading skeleton-loading--list">
          {Array.from({ length: rows }).map((_, index) => (
            <div key={index} className="skeleton-list-item">
              <Skeleton active avatar={avatar} paragraph={{ rows: 1 }} />
            </div>
          ))}
        </div>
      );
    
    case 'table':
      return (
        <div className="skeleton-loading skeleton-loading--table">
          <Skeleton active paragraph={{ rows: rows + 1 }} />
        </div>
      );
    
    case 'chat':
      return (
        <div className="skeleton-loading skeleton-loading--chat">
          {Array.from({ length: rows }).map((_, index) => (
            <div key={index} className={`skeleton-chat-message ${index % 2 === 0 ? 'skeleton-chat-message--user' : 'skeleton-chat-message--bot'}`}>
              <Skeleton active avatar paragraph={{ rows: Math.floor(Math.random() * 3) + 1 }} />
            </div>
          ))}
        </div>
      );
    
    default:
      return <Skeleton active avatar={avatar} paragraph={{ rows }} />;
  }
};

// 进度加载组件
export const ProgressLoading: React.FC<{
  percent: number;
  status?: 'normal' | 'exception' | 'success';
  showInfo?: boolean;
  strokeColor?: string;
  size?: 'small' | 'default';
}> = ({ 
  percent, 
  status = 'normal',
  showInfo = true,
  strokeColor,
  size = 'default'
}) => (
  <div className="progress-loading">
    <Progress
      percent={percent}
      status={status}
      showInfo={showInfo}
      strokeColor={strokeColor}
      size={size}
    />
  </div>
);

// 按钮加载状态
export const ButtonLoading: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  loadingText?: string;
}> = ({ loading, children, loadingText }) => (
  <span className="button-loading">
    {loading && <LoadingOutlined className="button-loading__icon" />}
    <span className="button-loading__text">
      {loading && loadingText ? loadingText : children}
    </span>
  </span>
);

// 智能加载组件 - 根据加载时间自动切换显示方式
export const SmartLoading: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  skeleton?: React.ReactNode;
  delay?: number;
  tip?: string;
}> = ({ 
  loading, 
  children, 
  skeleton,
  delay = 300,
  tip = '加载中...'
}) => {
  const [showLoading, setShowLoading] = React.useState(false);
  const [showSkeleton, setShowSkeleton] = React.useState(false);

  React.useEffect(() => {
    let timer1: NodeJS.Timeout;
    let timer2: NodeJS.Timeout;

    if (loading) {
      // 延迟显示加载状态，避免闪烁
      timer1 = setTimeout(() => {
        setShowLoading(true);
      }, delay);

      // 如果加载时间较长，显示骨架屏
      timer2 = setTimeout(() => {
        setShowSkeleton(true);
      }, delay + 1000);
    } else {
      setShowLoading(false);
      setShowSkeleton(false);
    }

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, [loading, delay]);

  if (!loading) {
    return <>{children}</>;
  }

  if (showSkeleton && skeleton) {
    return <>{skeleton}</>;
  }

  if (showLoading) {
    return <ContentLoading tip={tip} transparent />;
  }

  return <>{children}</>;
};

// 懒加载组件
export const LazyLoading: React.FC<{
  height?: number | string;
  placeholder?: React.ReactNode;
}> = ({ 
  height = 200,
  placeholder 
}) => (
  <div className="lazy-loading" style={{ height }}>
    {placeholder || <ContentLoading height={height} transparent />}
  </div>
);

export default {
  PageLoading,
  ContentLoading,
  SkeletonLoading,
  ProgressLoading,
  ButtonLoading,
  SmartLoading,
  LazyLoading
};
