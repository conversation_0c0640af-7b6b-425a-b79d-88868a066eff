import React, { useState, useRef, useEffect } from 'react';
import { Input, InputProps, Tooltip } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined, ClearOutlined } from '@ant-design/icons';
import '../styles/EnhancedInput.css';

const { TextArea } = Input;

interface EnhancedInputProps extends Omit<InputProps, 'size'> {
  label?: string;
  helperText?: string;
  error?: string;
  success?: boolean;
  variant?: 'outlined' | 'filled' | 'standard';
  size?: 'small' | 'medium' | 'large';
  clearable?: boolean;
  showPasswordToggle?: boolean;
  autoResize?: boolean;
  maxLength?: number;
  showCount?: boolean;
  tooltip?: string;
  required?: boolean;
}

const EnhancedInput: React.FC<EnhancedInputProps> = ({
  label,
  helperText,
  error,
  success = false,
  variant = 'outlined',
  size = 'medium',
  clearable = false,
  showPasswordToggle = false,
  autoResize = false,
  maxLength,
  showCount = false,
  tooltip,
  required = false,
  type = 'text',
  value,
  onChange,
  onFocus,
  onBlur,
  className = '',
  ...props
}) => {
  const [focused, setFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [internalValue, setInternalValue] = useState(value || '');
  const inputRef = useRef<any>(null);

  useEffect(() => {
    setInternalValue(value || '');
  }, [value]);

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setFocused(true);
    if (onFocus) onFocus(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setFocused(false);
    if (onBlur) onBlur(e);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    if (onChange) onChange(e);
  };

  const handleClear = () => {
    const event = {
      target: { value: '' }
    } as React.ChangeEvent<HTMLInputElement>;
    setInternalValue('');
    if (onChange) onChange(event);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const getInputClass = () => {
    const classes = ['enhanced-input'];
    
    classes.push(`enhanced-input--${variant}`);
    classes.push(`enhanced-input--${size}`);
    
    if (focused) classes.push('enhanced-input--focused');
    if (error) classes.push('enhanced-input--error');
    if (success) classes.push('enhanced-input--success');
    if (props.disabled) classes.push('enhanced-input--disabled');
    
    return [...classes, className].join(' ');
  };

  const getContainerClass = () => {
    const classes = ['enhanced-input-container'];
    
    if (label) classes.push('enhanced-input-container--with-label');
    if (error || helperText) classes.push('enhanced-input-container--with-helper');
    
    return classes.join(' ');
  };

  const renderSuffix = () => {
    const suffixElements = [];

    if (clearable && internalValue && !props.disabled) {
      suffixElements.push(
        <ClearOutlined
          key="clear"
          className="enhanced-input__clear-icon"
          onClick={handleClear}
        />
      );
    }

    if (showPasswordToggle && (type === 'password' || showPassword)) {
      suffixElements.push(
        <span
          key="password-toggle"
          className="enhanced-input__password-toggle"
          onClick={togglePasswordVisibility}
        >
          {showPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
        </span>
      );
    }

    if (showCount && maxLength) {
      suffixElements.push(
        <span key="count" className="enhanced-input__count">
          {String(internalValue).length}/{maxLength}
        </span>
      );
    }

    return suffixElements.length > 0 ? (
      <div className="enhanced-input__suffix">
        {suffixElements}
      </div>
    ) : null;
  };

  const inputProps = {
    ...props,
    ref: inputRef,
    type: showPasswordToggle && showPassword ? 'text' : type,
    value: internalValue,
    onChange: handleChange,
    onFocus: handleFocus,
    onBlur: handleBlur,
    className: getInputClass(),
    maxLength,
    suffix: renderSuffix(),
  };

  const inputElement = autoResize ? (
    <TextArea {...inputProps} autoSize={{ minRows: 2, maxRows: 6 }} />
  ) : (
    <Input {...inputProps} />
  );

  const wrappedInput = tooltip ? (
    <Tooltip title={tooltip} placement="top">
      {inputElement}
    </Tooltip>
  ) : inputElement;

  return (
    <div className={getContainerClass()}>
      {label && (
        <label className="enhanced-input__label">
          {label}
          {required && <span className="enhanced-input__required">*</span>}
        </label>
      )}
      
      {wrappedInput}
      
      {(error || helperText) && (
        <div className="enhanced-input__helper">
          {error ? (
            <span className="enhanced-input__error-text">{error}</span>
          ) : (
            <span className="enhanced-input__helper-text">{helperText}</span>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedInput;
