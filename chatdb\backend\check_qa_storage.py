#!/usr/bin/env python3
"""
检查问答对存储状态
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.hybrid_retrieval_service import HybridRetrievalEngine
from app.core.config import settings

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def check_qa_storage():
    """检查问答对存储状态"""
    
    logger.info("🔍 开始检查问答对存储状态...")
    
    try:
        # 创建混合检索引擎
        engine = HybridRetrievalEngine()
        
        # 初始化引擎
        logger.info("📡 初始化混合检索引擎...")
        await engine.initialize()
        
        if not engine._initialized:
            logger.error("❌ 混合检索引擎初始化失败")
            return False
        
        logger.info("✅ 混合检索引擎初始化成功")
        
        # 检查Milvus中的数据
        logger.info("📊 检查Milvus向量数据库...")
        try:
            from pymilvus import Collection
            collection = Collection("qa_pairs")
            
            # 获取总数
            collection.load()
            total_count = collection.num_entities
            logger.info(f"   Milvus中问答对总数: {total_count}")
            
            if total_count > 0:
                # 获取最近的几条记录
                results = collection.query(
                    expr="",
                    output_fields=["id", "question", "sql", "connection_id", "created_at"],
                    limit=5
                )
                
                logger.info(f"   最近的{len(results)}条记录:")
                for i, result in enumerate(results):
                    logger.info(f"   {i+1}. ID: {result.get('id', 'N/A')}")
                    logger.info(f"      问题: {result.get('question', 'N/A')[:50]}...")
                    logger.info(f"      SQL: {result.get('sql', 'N/A')[:50]}...")
                    logger.info(f"      连接ID: {result.get('connection_id', 'N/A')}")
                    logger.info(f"      创建时间: {result.get('created_at', 'N/A')}")
                    logger.info("")
            else:
                logger.warning("⚠️ Milvus中没有找到问答对数据")
                
        except Exception as e:
            logger.error(f"❌ 检查Milvus失败: {str(e)}")
        
        # 检查Neo4j中的数据
        logger.info("🔗 检查Neo4j图数据库...")
        try:
            from neo4j import GraphDatabase
            
            uri = settings.NEO4J_URI
            user = settings.NEO4J_USER
            password = settings.NEO4J_PASSWORD
            
            driver = GraphDatabase.driver(uri, auth=(user, password))
            
            with driver.session() as session:
                # 查询QAPair节点
                result = session.run("""
                    MATCH (qa:QAPair)
                    RETURN count(qa) as total_count
                """)
                
                record = result.single()
                total_count = record["total_count"] if record else 0
                logger.info(f"   Neo4j中QAPair节点总数: {total_count}")
                
                if total_count > 0:
                    # 获取最近的几条记录
                    result = session.run("""
                        MATCH (qa:QAPair)
                        RETURN qa.id as id, qa.question as question, qa.sql as sql, 
                               qa.connection_id as connection_id, qa.created_at as created_at
                        ORDER BY qa.created_at DESC
                        LIMIT 5
                    """)
                    
                    records = list(result)
                    logger.info(f"   最近的{len(records)}条记录:")
                    for i, record in enumerate(records):
                        logger.info(f"   {i+1}. ID: {record.get('id', 'N/A')}")
                        logger.info(f"      问题: {record.get('question', 'N/A')[:50]}...")
                        logger.info(f"      SQL: {record.get('sql', 'N/A')[:50]}...")
                        logger.info(f"      连接ID: {record.get('connection_id', 'N/A')}")
                        logger.info(f"      创建时间: {record.get('created_at', 'N/A')}")
                        logger.info("")
                else:
                    logger.warning("⚠️ Neo4j中没有找到QAPair节点")
            
            driver.close()
            
        except Exception as e:
            logger.error(f"❌ 检查Neo4j失败: {str(e)}")
        
        # 检查SQLite中的数据（如果有的话）
        logger.info("💾 检查本地数据库...")
        try:
            import sqlite3
            import os
            
            # 查找可能的SQLite数据库文件
            possible_db_files = [
                "qa_pairs.db",
                "app.db",
                "chatdb.db",
                "../qa_pairs.db"
            ]
            
            for db_file in possible_db_files:
                if os.path.exists(db_file):
                    logger.info(f"   找到数据库文件: {db_file}")
                    
                    conn = sqlite3.connect(db_file)
                    cursor = conn.cursor()
                    
                    # 查看所有表
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    logger.info(f"   数据库表: {[table[0] for table in tables]}")
                    
                    # 查找问答对相关的表
                    for table in tables:
                        table_name = table[0]
                        if 'qa' in table_name.lower() or 'question' in table_name.lower():
                            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                            count = cursor.fetchone()[0]
                            logger.info(f"   表 {table_name} 记录数: {count}")
                    
                    conn.close()
            
            if not any(os.path.exists(f) for f in possible_db_files):
                logger.info("   没有找到本地SQLite数据库文件")
                
        except Exception as e:
            logger.error(f"❌ 检查SQLite失败: {str(e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查过程中出现错误: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False

async def main():
    logger.info("🚀 开始检查问答对存储状态...")
    
    success = await check_qa_storage()
    
    if success:
        logger.info("✅ 存储状态检查完成")
    else:
        logger.error("❌ 存储状态检查失败")

if __name__ == "__main__":
    asyncio.run(main())
