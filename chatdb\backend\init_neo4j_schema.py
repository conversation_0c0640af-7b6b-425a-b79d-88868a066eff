#!/usr/bin/env python3
"""
初始化Neo4j数据库结构
用于消除启动时的警告信息
"""

from neo4j import GraphDatabase
import logging
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_neo4j_schema():
    """初始化Neo4j数据库结构"""
    
    # Neo4j连接配置
    uri = os.getenv("NEO4J_URI", "neo4j://localhost:7687")
    user = os.getenv("NEO4J_USER", "neo4j")
    password = os.getenv("NEO4J_PASSWORD", "Di@nhua11")
    
    try:
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        with driver.session() as session:
            logger.info("开始初始化Neo4j数据库结构...")
            
            # 创建约束和索引
            constraints_and_indexes = [
                # 为Table节点创建唯一约束
                "CREATE CONSTRAINT table_id_unique IF NOT EXISTS FOR (t:Table) REQUIRE t.id IS UNIQUE",
                
                # 为Column节点创建唯一约束  
                "CREATE CONSTRAINT column_id_unique IF NOT EXISTS FOR (c:Column) REQUIRE c.id IS UNIQUE",
                
                # 为QAPair节点创建唯一约束
                "CREATE CONSTRAINT qa_id_unique IF NOT EXISTS FOR (qa:QAPair) REQUIRE qa.id IS UNIQUE",
                
                # 为QueryPattern节点创建唯一约束
                "CREATE CONSTRAINT pattern_name_unique IF NOT EXISTS FOR (p:QueryPattern) REQUIRE p.name IS UNIQUE",
                
                # 创建索引以提高查询性能
                "CREATE INDEX table_connection_id IF NOT EXISTS FOR (t:Table) ON (t.connection_id)",
                "CREATE INDEX qa_connection_id IF NOT EXISTS FOR (qa:QAPair) ON (qa.connection_id)",
                "CREATE INDEX qa_query_type IF NOT EXISTS FOR (qa:QAPair) ON (qa.query_type)",
            ]
            
            for constraint in constraints_and_indexes:
                try:
                    session.run(constraint)
                    logger.info(f"✅ 执行成功: {constraint}")
                except Exception as e:
                    logger.warning(f"⚠️ 约束/索引可能已存在: {str(e)}")
            
            # 创建示例节点（可选，用于消除警告）
            sample_data = [
                """
                MERGE (t:Table {
                    id: 'sample_table_1',
                    name: 'sample_table',
                    connection_id: 0
                })
                """,
                
                """
                MERGE (c:Column {
                    id: 'sample_column_1',
                    name: 'sample_column',
                    table_name: 'sample_table',
                    data_type: 'VARCHAR'
                })
                """,
                
                """
                MATCH (t:Table {id: 'sample_table_1'})
                MATCH (c:Column {id: 'sample_column_1'})
                MERGE (t)-[:HAS_COLUMN]->(c)
                """,
                
                """
                MERGE (p:QueryPattern {
                    name: 'SELECT',
                    difficulty_level: 1,
                    usage_count: 0
                })
                """
            ]
            
            for query in sample_data:
                try:
                    session.run(query)
                    logger.info("✅ 创建示例数据成功")
                except Exception as e:
                    logger.warning(f"⚠️ 示例数据创建失败: {str(e)}")
            
            logger.info("🎉 Neo4j数据库结构初始化完成!")
            
    except Exception as e:
        logger.error(f"❌ Neo4j初始化失败: {str(e)}")
        logger.info("💡 提示：请确保Neo4j服务正在运行，并且连接配置正确")
        return False
    
    finally:
        if 'driver' in locals():
            driver.close()
    
    return True

if __name__ == "__main__":
    logger.info("开始初始化Neo4j数据库结构...")
    success = init_neo4j_schema()
    
    if success:
        logger.info("✅ 初始化成功! 重启服务后警告信息将消失")
    else:
        logger.error("❌ 初始化失败! 但这不影响系统正常使用")
