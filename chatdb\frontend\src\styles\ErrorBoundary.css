/* 错误边界样式 */

.error-boundary {
  padding: 24px;
  border-radius: 8px;
  background: white;
}

.error-boundary--page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.error-boundary--component {
  margin: 16px 0;
  border: 1px solid #ffccc7;
  background: #fff2f0;
}

.error-details {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.error-details-collapse {
  margin-top: 12px;
}

.error-stack {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  margin: 8px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-component-stack {
  margin-top: 12px;
}

.error-component-stack .ant-typography {
  display: block;
  margin-top: 4px;
}

/* 错误图标动画 */
.error-boundary .anticon {
  animation: errorPulse 2s ease-in-out infinite;
}

@keyframes errorPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* 按钮样式增强 */
.error-boundary .ant-btn {
  margin: 0 8px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.error-boundary .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.error-boundary .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
}

.error-boundary .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-boundary {
    padding: 16px;
  }
  
  .error-boundary--page {
    padding: 24px 16px;
  }
  
  .error-boundary .ant-result-title {
    font-size: 20px;
  }
  
  .error-boundary .ant-result-subtitle {
    font-size: 14px;
  }
  
  .error-boundary .ant-btn {
    margin: 4px;
    width: calc(50% - 8px);
  }
  
  .error-stack {
    font-size: 11px;
    padding: 8px;
  }
}

/* 暗色主题 */
[data-theme="dark"] .error-boundary {
  background: #1f1f1f;
  color: white;
}

[data-theme="dark"] .error-boundary--page {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

[data-theme="dark"] .error-boundary--component {
  background: #2a1f1f;
  border-color: #5c2c2c;
}

[data-theme="dark"] .error-details {
  background: #262626;
  border-color: #434343;
}

[data-theme="dark"] .error-stack {
  background: #1a1a1a;
  color: #e6e6e6;
}

/* 高对比度模式 */
[data-theme="high-contrast"] .error-boundary {
  background: white;
  border: 2px solid black;
}

[data-theme="high-contrast"] .error-boundary--component {
  background: white;
  border: 2px solid #cc0000;
}

[data-theme="high-contrast"] .error-details {
  background: #f0f0f0;
  border: 1px solid black;
}

[data-theme="high-contrast"] .error-stack {
  background: white;
  border: 1px solid black;
  color: black;
}

[data-theme="high-contrast"] .ant-btn-primary {
  background: #0066cc !important;
  border-color: #0066cc !important;
  color: white !important;
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .error-boundary .anticon {
    animation: none;
  }
  
  .error-boundary .ant-btn {
    transition: none;
  }
  
  .error-boundary .ant-btn:hover {
    transform: none;
  }
}

/* 无障碍访问 */
.error-boundary:focus-within {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.error-boundary .ant-btn:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* 打印样式 */
@media print {
  .error-boundary--page {
    background: white;
    color: black;
  }
  
  .error-boundary .ant-btn {
    display: none;
  }
  
  .error-stack {
    background: white;
    border: 1px solid black;
    color: black;
  }
}
