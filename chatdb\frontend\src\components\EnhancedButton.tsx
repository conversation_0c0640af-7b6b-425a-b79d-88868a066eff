import React from 'react';
import { Button, ButtonProps, Tooltip } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import '../styles/EnhancedButton.css';

interface EnhancedButtonProps extends ButtonProps {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  tooltip?: string;
  loading?: boolean;
  loadingText?: string;
  ripple?: boolean;
  elevated?: boolean;
  fullWidth?: boolean;
}

const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  tooltip,
  loading = false,
  loadingText,
  ripple = true,
  elevated = false,
  fullWidth = false,
  children,
  className = '',
  onClick,
  ...props
}) => {
  const handleClick = (e: React.MouseEvent<HTMLElement>) => {
    if (ripple && !loading && !props.disabled) {
      createRipple(e);
    }
    if (onClick) {
      onClick(e);
    }
  };

  const createRipple = (event: React.MouseEvent<HTMLElement>) => {
    const button = event.currentTarget;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    const ripple = document.createElement('span');
    ripple.className = 'button-ripple';
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';

    button.appendChild(ripple);

    setTimeout(() => {
      ripple.remove();
    }, 600);
  };

  const getButtonClass = () => {
    const classes = ['enhanced-button'];
    
    classes.push(`enhanced-button--${variant}`);
    classes.push(`enhanced-button--${size}`);
    
    if (elevated) classes.push('enhanced-button--elevated');
    if (fullWidth) classes.push('enhanced-button--full-width');
    if (loading) classes.push('enhanced-button--loading');
    if (ripple) classes.push('enhanced-button--ripple');
    
    return [...classes, className].join(' ');
  };

  const buttonContent = (
    <Button
      {...props}
      className={getButtonClass()}
      onClick={handleClick}
      disabled={props.disabled || loading}
    >
      {loading && <LoadingOutlined className="enhanced-button__loading-icon" />}
      <span className="enhanced-button__content">
        {loading && loadingText ? loadingText : children}
      </span>
    </Button>
  );

  if (tooltip) {
    return (
      <Tooltip title={tooltip} placement="top">
        {buttonContent}
      </Tooltip>
    );
  }

  return buttonContent;
};

export default EnhancedButton;
