#!/usr/bin/env python3
"""
测试修复后的BGE服务调用
"""

import asyncio
import httpx
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.hybrid_retrieval_service import VectorService
from app.core.config import settings

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_fixed_bge_service():
    """测试修复后的BGE服务"""
    
    logger.info("🧪 开始测试修复后的BGE服务...")
    
    try:
        # 创建向量服务实例
        vector_service = VectorService()
        
        # 初始化服务
        logger.info("📡 初始化向量服务...")
        await vector_service.initialize()
        
        if not vector_service.use_local_service:
            logger.error("❌ 本地BGE服务未启用")
            return False
        
        logger.info(f"✅ 向量服务初始化成功，使用本地服务: {vector_service.bge_service_url}")
        
        # 测试单个文本向量化
        logger.info("🔤 测试单个文本向量化...")
        test_text = "这是一个测试问题，用于验证BGE服务是否正常工作"
        
        embedding = await vector_service.embed_question(test_text)
        
        if embedding and len(embedding) > 0:
            logger.info(f"✅ 单个文本向量化成功!")
            logger.info(f"   📊 向量维度: {len(embedding)}")
            logger.info(f"   🔢 前5个值: {embedding[:5]}")
        else:
            logger.error("❌ 单个文本向量化失败 - 返回空向量")
            return False
        
        # 测试批量向量化
        logger.info("📚 测试批量向量化...")
        test_texts = [
            "第一个测试问题",
            "第二个测试问题", 
            "第三个测试问题"
        ]
        
        embeddings = await vector_service.batch_embed(test_texts)
        
        if embeddings and len(embeddings) == len(test_texts):
            logger.info(f"✅ 批量向量化成功!")
            logger.info(f"   📊 处理文本数量: {len(embeddings)}")
            logger.info(f"   📏 每个向量维度: {len(embeddings[0]) if embeddings else 0}")
        else:
            logger.error(f"❌ 批量向量化失败 - 期望{len(test_texts)}个向量，实际得到{len(embeddings) if embeddings else 0}个")
            return False
        
        logger.info("🎉 所有测试通过! BGE服务修复成功!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False

async def test_direct_api_call():
    """直接测试BGE API调用"""
    logger.info("🔗 直接测试BGE API调用...")
    
    try:
        async with httpx.AsyncClient() as client:
            # 测试单个文本
            response = await client.post(
                "http://localhost:8080/embed",
                json={"inputs": "测试文本"},
                timeout=10.0
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 直接API调用成功!")
                logger.info(f"   📊 响应类型: {type(result)}")
                logger.info(f"   📏 向量维度: {len(result[0]) if isinstance(result, list) and result else 'N/A'}")
                return True
            else:
                logger.error(f"❌ 直接API调用失败: {response.status_code} - {response.text}")
                return False
                
    except Exception as e:
        logger.error(f"❌ 直接API调用出错: {str(e)}")
        return False

async def main():
    logger.info("🚀 开始BGE服务修复验证...")
    
    # 测试直接API调用
    direct_success = await test_direct_api_call()
    
    if not direct_success:
        logger.error("💥 直接API调用失败，请检查BGE服务状态")
        return
    
    # 测试修复后的服务
    service_success = await test_fixed_bge_service()
    
    if service_success:
        logger.info("🎊 恭喜! BGE服务修复成功，现在可以正常创建问答对了!")
        logger.info("💡 请重启后端服务，然后尝试创建问答对")
    else:
        logger.error("💥 BGE服务修复失败，需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
