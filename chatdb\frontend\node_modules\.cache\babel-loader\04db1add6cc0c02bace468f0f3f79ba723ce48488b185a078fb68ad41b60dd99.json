{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\pages\\\\HybridQA\\\\index.tsx\",\n  _s = $RefreshSig$();\n// 混合问答对管理页面\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Tag, Modal, Form, Input, Select, InputNumber, Switch, message, Tabs, Row, Col, Statistic, Progress, Tooltip, Divider } from 'antd';\nimport { PlusOutlined, SearchOutlined, ExportOutlined, ImportOutlined, DeleteOutlined, EyeOutlined, RobotOutlined, DatabaseOutlined, BulbOutlined, QuestionCircleOutlined } from '@ant-design/icons';\nimport { hybridQAService } from '../../services/hybridQA';\nimport { getConnections } from '../../services/api';\nimport QAFeedbackModal from '../../components/QAFeedbackModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TabPane\n} = Tabs;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst HybridQAPage = () => {\n  _s();\n  var _selectedQAPair$used_, _selectedQAPair$menti;\n  const [qaPairs, setQaPairs] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [searchModalVisible, setSearchModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);\n  const [selectedQAPair, setSelectedQAPair] = useState(null);\n  const [searchResults, setSearchResults] = useState([]);\n  const [stats, setStats] = useState({});\n  const [connections, setConnections] = useState([]);\n  const [selectedConnectionId, setSelectedConnectionId] = useState(null);\n  const [loadingConnections, setLoadingConnections] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n  useEffect(() => {\n    loadConnections();\n    loadStats();\n    loadQAPairs();\n  }, []);\n  useEffect(() => {\n    if (selectedConnectionId) {\n      loadStats(selectedConnectionId);\n      loadQAPairs(selectedConnectionId);\n    }\n  }, [selectedConnectionId]);\n  const loadConnections = async () => {\n    try {\n      setLoadingConnections(true);\n      const response = await getConnections();\n      setConnections(response.data);\n    } catch (error) {\n      message.error('获取数据库连接失败');\n      console.error('获取连接失败:', error);\n    } finally {\n      setLoadingConnections(false);\n    }\n  };\n  const loadQAPairs = async connectionId => {\n    try {\n      setLoading(true);\n      const response = await hybridQAService.getQAPairs(connectionId);\n      setQaPairs(response);\n    } catch (error) {\n      console.error('加载问答对列表失败:', error);\n      message.error('加载问答对列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStats = async connectionId => {\n    try {\n      const response = await hybridQAService.getStats(connectionId);\n      setStats(response);\n    } catch (error) {\n      console.error('加载统计信息失败:', error);\n    }\n  };\n  const handleCreateQAPair = async values => {\n    try {\n      setLoading(true);\n      await hybridQAService.createQAPair(values);\n      message.success('问答对创建成功');\n      setCreateModalVisible(false);\n      form.resetFields();\n      loadStats(selectedConnectionId || undefined); // 重新加载统计信息\n      loadQAPairs(selectedConnectionId || undefined); // 重新加载问答对列表\n    } catch (error) {\n      message.error('创建失败');\n      console.error('创建问答对失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearchSimilar = async values => {\n    try {\n      setLoading(true);\n      const results = await hybridQAService.searchSimilar({\n        question: values.question,\n        connection_id: values.connection_id || selectedConnectionId,\n        top_k: values.top_k || 5\n      });\n      setSearchResults(results);\n    } catch (error) {\n      message.error('搜索失败');\n      console.error('搜索相似问答对失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleViewDetail = qaPair => {\n    setSelectedQAPair(qaPair);\n    setDetailModalVisible(true);\n  };\n  const getQueryTypeColor = type => {\n    const colors = {\n      'SELECT': 'blue',\n      'JOIN': 'green',\n      'AGGREGATE': 'orange',\n      'GROUP_BY': 'purple',\n      'ORDER_BY': 'cyan'\n    };\n    return colors[type] || 'default';\n  };\n  const getDifficultyColor = level => {\n    if (level <= 2) return 'green';\n    if (level <= 3) return 'orange';\n    return 'red';\n  };\n  const searchColumns = [{\n    title: '问题',\n    dataIndex: ['qa_pair', 'question'],\n    key: 'question',\n    ellipsis: true,\n    width: 300\n  }, {\n    title: 'SQL',\n    dataIndex: ['qa_pair', 'sql'],\n    key: 'sql',\n    ellipsis: true,\n    width: 400,\n    render: sql => /*#__PURE__*/_jsxDEV(\"code\", {\n      style: {\n        fontSize: '12px',\n        background: '#f5f5f5',\n        padding: '2px 4px'\n      },\n      children: sql\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '查询类型',\n    dataIndex: ['qa_pair', 'query_type'],\n    key: 'query_type',\n    width: 100,\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getQueryTypeColor(type),\n      children: type\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '数据库连接',\n    dataIndex: ['qa_pair', 'connection_id'],\n    key: 'connection_id',\n    width: 120,\n    render: connectionId => {\n      const connection = connections.find(conn => conn.id === connectionId);\n      return connection ? /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: `${connection.db_type} - ${connection.database_name}`,\n        children: /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          children: connection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"default\",\n        children: [\"ID: \", connectionId]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '相关度',\n    dataIndex: 'final_score',\n    key: 'final_score',\n    width: 120,\n    render: score => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: Math.round(score * 100),\n      size: \"small\",\n      status: score > 0.8 ? 'success' : score > 0.6 ? 'normal' : 'exception'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '推荐理由',\n    dataIndex: 'explanation',\n    key: 'explanation',\n    ellipsis: true,\n    width: 200\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleViewDetail(record.qa_pair)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        onClick: () => {\n          setSelectedQAPair(record.qa_pair);\n          setFeedbackModalVisible(true);\n        },\n        children: \"\\u53CD\\u9988\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6DF7\\u5408\\u68C0\\u7D22\\u95EE\\u7B54\\u5BF9\\u7BA1\\u7406\",\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: '8px'\n            },\n            children: \"\\u6570\\u636E\\u5E93\\u8FDE\\u63A5:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\",\n            value: selectedConnectionId,\n            onChange: setSelectedConnectionId,\n            loading: loadingConnections,\n            style: {\n              width: 300\n            },\n            allowClear: true,\n            children: connections.map(conn => /*#__PURE__*/_jsxDEV(Option, {\n              value: conn.id,\n              children: [conn.name, \" (\", conn.db_type, \" - \", conn.database_name, \")\"]\n            }, conn.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u9009\\u62E9\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\\u540E\\uFF0C\\u7EDF\\u8BA1\\u4FE1\\u606F\\u548C\\u641C\\u7D22\\u7ED3\\u679C\\u5C06\\u4EC5\\u663E\\u793A\\u8BE5\\u8FDE\\u63A5\\u4E0B\\u7684\\u95EE\\u7B54\\u5BF9\",\n          children: /*#__PURE__*/_jsxDEV(QuestionCircleOutlined, {\n            style: {\n              color: '#999'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u95EE\\u7B54\\u5BF9\\u6570\",\n            value: stats.total_qa_pairs || 0,\n            prefix: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u9A8C\\u8BC1\\u6570\",\n            value: stats.verified_qa_pairs || 0,\n            prefix: /*#__PURE__*/_jsxDEV(BulbOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u6210\\u529F\\u7387\",\n            value: stats.average_success_rate || 0,\n            precision: 2,\n            suffix: \"%\",\n            prefix: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9A8C\\u8BC1\\u7387\",\n            value: stats.total_qa_pairs > 0 ? stats.verified_qa_pairs / stats.total_qa_pairs * 100 : 0,\n            precision: 1,\n            suffix: \"%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        style: {\n          marginBottom: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 19\n          }, this),\n          onClick: () => {\n            setCreateModalVisible(true);\n            // 如果选择了连接，设置为默认值\n            if (selectedConnectionId) {\n              form.setFieldsValue({\n                connection_id: selectedConnectionId\n              });\n            }\n          },\n          children: \"\\u521B\\u5EFA\\u95EE\\u7B54\\u5BF9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 19\n          }, this),\n          onClick: () => {\n            setSearchModalVisible(true);\n            // 如果选择了连接，设置为默认值\n            if (selectedConnectionId) {\n              searchForm.setFieldsValue({\n                connection_id: selectedConnectionId\n              });\n            }\n          },\n          children: \"\\u667A\\u80FD\\u641C\\u7D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ImportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 25\n          }, this),\n          children: \"\\u6279\\u91CF\\u5BFC\\u5165\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5BFC\\u51FA\\u6570\\u636E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        defaultActiveKey: \"list\",\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u95EE\\u7B54\\u5BF9\\u5217\\u8868\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: [{\n              title: 'ID',\n              dataIndex: 'id',\n              key: 'id',\n              width: 150,\n              render: text => /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontFamily: 'monospace',\n                  fontSize: '12px'\n                },\n                children: [text.substring(0, 12), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 21\n              }, this)\n            }, {\n              title: '问题',\n              dataIndex: 'question',\n              key: 'question',\n              width: 300,\n              ellipsis: true\n            }, {\n              title: 'SQL',\n              dataIndex: 'sql',\n              key: 'sql',\n              width: 300,\n              ellipsis: true,\n              render: text => /*#__PURE__*/_jsxDEV(\"code\", {\n                style: {\n                  fontSize: '12px',\n                  background: '#f5f5f5',\n                  padding: '2px 4px'\n                },\n                children: text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 21\n              }, this)\n            }, {\n              title: '连接ID',\n              dataIndex: 'connection_id',\n              key: 'connection_id',\n              width: 80\n            }, {\n              title: '查询类型',\n              dataIndex: 'query_type',\n              key: 'query_type',\n              width: 100,\n              render: type => /*#__PURE__*/_jsxDEV(Tag, {\n                color: getQueryTypeColor(type),\n                children: type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this)\n            }, {\n              title: '难度',\n              dataIndex: 'difficulty_level',\n              key: 'difficulty_level',\n              width: 80,\n              render: level => /*#__PURE__*/_jsxDEV(Tag, {\n                color: getDifficultyColor(level),\n                children: level\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 21\n              }, this)\n            }, {\n              title: '成功率',\n              dataIndex: 'success_rate',\n              key: 'success_rate',\n              width: 100,\n              render: rate => `${(rate * 100).toFixed(1)}%`\n            }, {\n              title: '已验证',\n              dataIndex: 'verified',\n              key: 'verified',\n              width: 80,\n              render: verified => /*#__PURE__*/_jsxDEV(Tag, {\n                color: verified ? 'green' : 'orange',\n                children: verified ? '是' : '否'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 21\n              }, this)\n            }, {\n              title: '创建时间',\n              dataIndex: 'created_at',\n              key: 'created_at',\n              width: 150,\n              render: date => new Date(date).toLocaleString()\n            }, {\n              title: '操作',\n              key: 'action',\n              width: 120,\n              render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"text\",\n                    icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 33\n                    }, this),\n                    onClick: () => handleViewDetail(record)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u5220\\u9664\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"text\",\n                    danger: true,\n                    icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 33\n                    }, this),\n                    onClick: () => {\n                      // TODO: 实现删除功能\n                      message.info('删除功能开发中');\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 21\n              }, this)\n            }],\n            dataSource: qaPairs,\n            loading: loading,\n            rowKey: \"id\",\n            pagination: {\n              pageSize: 10,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: total => `共 ${total} 条记录`\n            },\n            scroll: {\n              x: 1400\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, \"list\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u667A\\u80FD\\u641C\\u7D22\",\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            style: {\n              marginBottom: '16px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              form: searchForm,\n              layout: \"inline\",\n              onFinish: handleSearchSimilar,\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"question\",\n                rules: [{\n                  required: true,\n                  message: '请输入问题'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8F93\\u5165\\u81EA\\u7136\\u8BED\\u8A00\\u95EE\\u9898\",\n                  style: {\n                    width: 300\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"connection_id\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u9009\\u62E9\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\",\n                  style: {\n                    width: 200\n                  },\n                  allowClear: true,\n                  children: connections.map(conn => /*#__PURE__*/_jsxDEV(Option, {\n                    value: conn.id,\n                    children: conn.name\n                  }, conn.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"top_k\",\n                initialValue: 5,\n                children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                  placeholder: \"\\u8FD4\\u56DE\\u6570\\u91CF\",\n                  min: 1,\n                  max: 20,\n                  style: {\n                    width: 100\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  loading: loading,\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 27\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            columns: searchColumns,\n            dataSource: searchResults,\n            loading: loading,\n            rowKey: record => record.qa_pair.id,\n            pagination: {\n              pageSize: 10,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: total => `共 ${total} 条记录`\n            },\n            scroll: {\n              x: 1200\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)]\n        }, \"search\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u7EDF\\u8BA1\\u5206\\u6790\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u67E5\\u8BE2\\u7C7B\\u578B\\u5206\\u5E03\",\n                size: \"small\",\n                children: stats.query_types && Object.entries(stats.query_types).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Tag, {\n                      color: getQueryTypeColor(type),\n                      style: {\n                        fontSize: '12px'\n                      },\n                      children: type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 555,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 556,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                    percent: stats.total_qa_pairs > 0 ? count / stats.total_qa_pairs * 100 : 0,\n                    size: \"small\",\n                    showInfo: false\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 23\n                  }, this)]\n                }, type, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u96BE\\u5EA6\\u5206\\u5E03\",\n                size: \"small\",\n                children: stats.difficulty_distribution && Object.entries(stats.difficulty_distribution).map(([level, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"\\u96BE\\u5EA6 \", level]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                    percent: stats.total_qa_pairs > 0 ? count / stats.total_qa_pairs * 100 : 0,\n                    size: \"small\",\n                    showInfo: false,\n                    strokeColor: getDifficultyColor(parseInt(level))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 23\n                  }, this)]\n                }, level, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this)\n        }, \"stats\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u521B\\u5EFA\\u95EE\\u7B54\\u5BF9\",\n      open: createModalVisible,\n      onCancel: () => setCreateModalVisible(false),\n      footer: null,\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleCreateQAPair,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"question\",\n          label: \"\\u81EA\\u7136\\u8BED\\u8A00\\u95EE\\u9898\",\n          rules: [{\n            required: true,\n            message: '请输入问题'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8F93\\u5165\\u81EA\\u7136\\u8BED\\u8A00\\u95EE\\u9898\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"sql\",\n          label: \"SQL\\u8BED\\u53E5\",\n          rules: [{\n            required: true,\n            message: '请输入SQL语句'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 5,\n            placeholder: \"\\u8F93\\u5165\\u5BF9\\u5E94\\u7684SQL\\u8BED\\u53E5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"connection_id\",\n              label: \"\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\",\n              rules: [{\n                required: true,\n                message: '请选择数据库连接'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u9009\\u62E9\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\",\n                loading: loadingConnections,\n                children: connections.map(conn => /*#__PURE__*/_jsxDEV(Option, {\n                  value: conn.id,\n                  children: [conn.name, \" (\", conn.db_type, \")\"]\n                }, conn.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"query_type\",\n              label: \"\\u67E5\\u8BE2\\u7C7B\\u578B\",\n              initialValue: \"SELECT\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"SELECT\",\n                  children: \"SELECT\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"JOIN\",\n                  children: \"JOIN\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"AGGREGATE\",\n                  children: \"AGGREGATE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"GROUP_BY\",\n                  children: \"GROUP_BY\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"ORDER_BY\",\n                  children: \"ORDER_BY\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"difficulty_level\",\n              label: \"\\u96BE\\u5EA6\\u7B49\\u7EA7\",\n              initialValue: 3,\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"verified\",\n          label: \"\\u5DF2\\u9A8C\\u8BC1\",\n          valuePropName: \"checked\",\n          initialValue: false,\n          children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              children: \"\\u521B\\u5EFA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setCreateModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u95EE\\u7B54\\u5BF9\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 693,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: selectedQAPair && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u57FA\\u672C\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ID:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 20\n              }, this), \" \", selectedQAPair.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u67E5\\u8BE2\\u7C7B\\u578B:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 20\n              }, this), \" \", /*#__PURE__*/_jsxDEV(Tag, {\n                color: getQueryTypeColor(selectedQAPair.query_type),\n                children: selectedQAPair.query_type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 43\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u96BE\\u5EA6\\u7B49\\u7EA7:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 20\n              }, this), \" \", selectedQAPair.difficulty_level]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u6570\\u636E\\u5E93\\u8FDE\\u63A5:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 20\n              }, this), \" \", (() => {\n                const connection = connections.find(conn => conn.id === selectedQAPair.connection_id);\n                return connection ? /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: [connection.name, \" (\", connection.db_type, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"default\",\n                  children: [\"ID: \", selectedQAPair.connection_id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 21\n                }, this);\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u6210\\u529F\\u7387:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 20\n              }, this), \" \", (selectedQAPair.success_rate * 100).toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u5DF2\\u9A8C\\u8BC1:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 20\n              }, this), \" \", selectedQAPair.verified ? '是' : '否']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 20\n              }, this), \" \", new Date(selectedQAPair.created_at).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u95EE\\u9898\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            background: '#f5f5f5',\n            padding: '12px',\n            borderRadius: '4px'\n          },\n          children: selectedQAPair.question\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"SQL\\u8BED\\u53E5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            background: '#f5f5f5',\n            padding: '12px',\n            borderRadius: '4px',\n            overflow: 'auto'\n          },\n          children: selectedQAPair.sql\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 729,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u4F7F\\u7528\\u7684\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 733,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: (_selectedQAPair$used_ = selectedQAPair.used_tables) === null || _selectedQAPair$used_ === void 0 ? void 0 : _selectedQAPair$used_.map((table, index) => /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: table\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"left\",\n          children: \"\\u63D0\\u53CA\\u7684\\u5B9E\\u4F53\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: (_selectedQAPair$menti = selectedQAPair.mentioned_entities) === null || _selectedQAPair$menti === void 0 ? void 0 : _selectedQAPair$menti.map((entity, index) => /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"green\",\n            children: entity\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 700,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 688,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(QAFeedbackModal, {\n      visible: feedbackModalVisible,\n      onCancel: () => setFeedbackModalVisible(false),\n      qaPair: selectedQAPair,\n      onFeedbackSubmitted: () => {\n        // 重新加载统计信息\n        loadStats(selectedConnectionId || undefined);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 751,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 262,\n    columnNumber: 5\n  }, this);\n};\n_s(HybridQAPage, \"B5jGAzCgK2KsoApxgj0/NhZFhZM=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = HybridQAPage;\nexport default HybridQAPage;\nvar _c;\n$RefreshReg$(_c, \"HybridQAPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Tag", "Modal", "Form", "Input", "Select", "InputNumber", "Switch", "message", "Tabs", "Row", "Col", "Statistic", "Progress", "<PERSON><PERSON><PERSON>", "Divider", "PlusOutlined", "SearchOutlined", "ExportOutlined", "ImportOutlined", "DeleteOutlined", "EyeOutlined", "RobotOutlined", "DatabaseOutlined", "BulbOutlined", "QuestionCircleOutlined", "hybridQAService", "getConnections", "QAFeedbackModal", "jsxDEV", "_jsxDEV", "TabPane", "TextArea", "Option", "HybridQAPage", "_s", "_selectedQAPair$used_", "_selectedQAPair$menti", "qaPairs", "setQaPairs", "loading", "setLoading", "createModalVisible", "setCreateModalVisible", "searchModalVisible", "setSearchModalVisible", "detailModalVisible", "setDetailModalVisible", "feedbackModalVisible", "setFeedbackModalVisible", "selectedQAPair", "setSelectedQAPair", "searchResults", "setSearchResults", "stats", "setStats", "connections", "setConnections", "selectedConnectionId", "setSelectedConnectionId", "loadingConnections", "setLoadingConnections", "form", "useForm", "searchForm", "loadConnections", "loadStats", "loadQAPairs", "response", "data", "error", "console", "connectionId", "getQAPairs", "getStats", "handleCreateQAPair", "values", "createQAPair", "success", "resetFields", "undefined", "handleSearchSimilar", "results", "searchSimilar", "question", "connection_id", "top_k", "handleViewDetail", "qa<PERSON>air", "getQueryTypeColor", "type", "colors", "getDifficultyColor", "level", "searchColumns", "title", "dataIndex", "key", "ellipsis", "width", "render", "sql", "style", "fontSize", "background", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "connection", "find", "conn", "id", "db_type", "database_name", "name", "score", "percent", "Math", "round", "size", "status", "_", "record", "icon", "onClick", "qa_pair", "marginBottom", "display", "justifyContent", "alignItems", "marginRight", "placeholder", "value", "onChange", "allowClear", "map", "gutter", "span", "total_qa_pairs", "prefix", "verified_qa_pairs", "average_success_rate", "precision", "suffix", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultActiveKey", "tab", "columns", "text", "fontFamily", "substring", "rate", "toFixed", "verified", "date", "Date", "toLocaleString", "danger", "info", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "scroll", "x", "layout", "onFinish", "<PERSON><PERSON>", "rules", "required", "initialValue", "min", "max", "htmlType", "query_types", "Object", "entries", "count", "showInfo", "difficulty_distribution", "strokeColor", "parseInt", "open", "onCancel", "footer", "label", "rows", "valuePropName", "orientation", "query_type", "difficulty_level", "success_rate", "created_at", "borderRadius", "overflow", "used_tables", "table", "index", "mentioned_entities", "entity", "visible", "onFeedbackSubmitted", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/pages/HybridQA/index.tsx"], "sourcesContent": ["// 混合问答对管理页面\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  InputNumber,\n  Switch,\n  message,\n  Tabs,\n  Row,\n  Col,\n  Statistic,\n  Progress,\n  Tooltip,\n  Divider\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  ExportOutlined,\n  ImportOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  EyeOutlined,\n  RobotOutlined,\n  DatabaseOutlined,\n  BulbOutlined,\n  QuestionCircleOutlined\n} from '@ant-design/icons';\nimport { hybridQAService } from '../../services/hybridQA';\nimport { getConnections } from '../../services/api';\nimport QAFeedbackModal from '../../components/QAFeedbackModal';\nimport type { QAPair, SimilarQAPair, QAPairCreate } from '../../types/hybridQA';\nimport type { DBConnection } from '../../types/api';\n\nconst { TabPane } = Tabs;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst HybridQAPage: React.FC = () => {\n  const [qaPairs, setQaPairs] = useState<QAPair[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [searchModalVisible, setSearchModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);\n  const [selectedQAPair, setSelectedQAPair] = useState<QAPair | null>(null);\n  const [searchResults, setSearchResults] = useState<SimilarQAPair[]>([]);\n  const [stats, setStats] = useState<any>({});\n  const [connections, setConnections] = useState<DBConnection[]>([]);\n  const [selectedConnectionId, setSelectedConnectionId] = useState<number | null>(null);\n  const [loadingConnections, setLoadingConnections] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  useEffect(() => {\n    loadConnections();\n    loadStats();\n    loadQAPairs();\n  }, []);\n\n  useEffect(() => {\n    if (selectedConnectionId) {\n      loadStats(selectedConnectionId);\n      loadQAPairs(selectedConnectionId);\n    }\n  }, [selectedConnectionId]);\n\n  const loadConnections = async () => {\n    try {\n      setLoadingConnections(true);\n      const response = await getConnections();\n      setConnections(response.data);\n    } catch (error) {\n      message.error('获取数据库连接失败');\n      console.error('获取连接失败:', error);\n    } finally {\n      setLoadingConnections(false);\n    }\n  };\n\n  const loadQAPairs = async (connectionId?: number) => {\n    try {\n      setLoading(true);\n      const response = await hybridQAService.getQAPairs(connectionId);\n      setQaPairs(response);\n    } catch (error) {\n      console.error('加载问答对列表失败:', error);\n      message.error('加载问答对列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStats = async (connectionId?: number) => {\n    try {\n      const response = await hybridQAService.getStats(connectionId);\n      setStats(response);\n    } catch (error) {\n      console.error('加载统计信息失败:', error);\n    }\n  };\n\n  const handleCreateQAPair = async (values: QAPairCreate) => {\n    try {\n      setLoading(true);\n      await hybridQAService.createQAPair(values);\n      message.success('问答对创建成功');\n      setCreateModalVisible(false);\n      form.resetFields();\n      loadStats(selectedConnectionId || undefined); // 重新加载统计信息\n      loadQAPairs(selectedConnectionId || undefined); // 重新加载问答对列表\n    } catch (error) {\n      message.error('创建失败');\n      console.error('创建问答对失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearchSimilar = async (values: any) => {\n    try {\n      setLoading(true);\n      const results = await hybridQAService.searchSimilar({\n        question: values.question,\n        connection_id: values.connection_id || selectedConnectionId,\n        top_k: values.top_k || 5\n      });\n      setSearchResults(results);\n    } catch (error) {\n      message.error('搜索失败');\n      console.error('搜索相似问答对失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleViewDetail = (qaPair: QAPair) => {\n    setSelectedQAPair(qaPair);\n    setDetailModalVisible(true);\n  };\n\n  const getQueryTypeColor = (type: string) => {\n    const colors: Record<string, string> = {\n      'SELECT': 'blue',\n      'JOIN': 'green',\n      'AGGREGATE': 'orange',\n      'GROUP_BY': 'purple',\n      'ORDER_BY': 'cyan'\n    };\n    return colors[type] || 'default';\n  };\n\n  const getDifficultyColor = (level: number) => {\n    if (level <= 2) return 'green';\n    if (level <= 3) return 'orange';\n    return 'red';\n  };\n\n  const searchColumns = [\n    {\n      title: '问题',\n      dataIndex: ['qa_pair', 'question'],\n      key: 'question',\n      ellipsis: true,\n      width: 300,\n    },\n    {\n      title: 'SQL',\n      dataIndex: ['qa_pair', 'sql'],\n      key: 'sql',\n      ellipsis: true,\n      width: 400,\n      render: (sql: string) => (\n        <code style={{ fontSize: '12px', background: '#f5f5f5', padding: '2px 4px' }}>\n          {sql}\n        </code>\n      ),\n    },\n    {\n      title: '查询类型',\n      dataIndex: ['qa_pair', 'query_type'],\n      key: 'query_type',\n      width: 100,\n      render: (type: string) => (\n        <Tag color={getQueryTypeColor(type)}>{type}</Tag>\n      ),\n    },\n    {\n      title: '数据库连接',\n      dataIndex: ['qa_pair', 'connection_id'],\n      key: 'connection_id',\n      width: 120,\n      render: (connectionId: number) => {\n        const connection = connections.find(conn => conn.id === connectionId);\n        return connection ? (\n          <Tooltip title={`${connection.db_type} - ${connection.database_name}`}>\n            <Tag color=\"blue\">{connection.name}</Tag>\n          </Tooltip>\n        ) : (\n          <Tag color=\"default\">ID: {connectionId}</Tag>\n        );\n      },\n    },\n    {\n      title: '相关度',\n      dataIndex: 'final_score',\n      key: 'final_score',\n      width: 120,\n      render: (score: number) => (\n        <Progress\n          percent={Math.round(score * 100)}\n          size=\"small\"\n          status={score > 0.8 ? 'success' : score > 0.6 ? 'normal' : 'exception'}\n        />\n      ),\n    },\n    {\n      title: '推荐理由',\n      dataIndex: 'explanation',\n      key: 'explanation',\n      ellipsis: true,\n      width: 200,\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      render: (_: any, record: SimilarQAPair) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewDetail(record.qa_pair)}\n            />\n          </Tooltip>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            onClick={() => {\n              setSelectedQAPair(record.qa_pair);\n              setFeedbackModalVisible(true);\n            }}\n          >\n            反馈\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Card title=\"混合检索问答对管理\" style={{ marginBottom: '24px' }}>\n        {/* 数据库连接选择器 */}\n        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <span style={{ marginRight: '8px' }}>数据库连接:</span>\n            <Select\n              placeholder=\"选择数据库连接（可选）\"\n              value={selectedConnectionId}\n              onChange={setSelectedConnectionId}\n              loading={loadingConnections}\n              style={{ width: 300 }}\n              allowClear\n            >\n              {connections.map(conn => (\n                <Option key={conn.id} value={conn.id}>\n                  {conn.name} ({conn.db_type} - {conn.database_name})\n                </Option>\n              ))}\n            </Select>\n          </div>\n          <Tooltip title=\"选择数据库连接后，统计信息和搜索结果将仅显示该连接下的问答对\">\n            <QuestionCircleOutlined style={{ color: '#999' }} />\n          </Tooltip>\n        </div>\n\n        <Row gutter={16} style={{ marginBottom: '24px' }}>\n          <Col span={6}>\n            <Statistic\n              title=\"总问答对数\"\n              value={stats.total_qa_pairs || 0}\n              prefix={<DatabaseOutlined />}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"已验证数\"\n              value={stats.verified_qa_pairs || 0}\n              prefix={<BulbOutlined />}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"平均成功率\"\n              value={stats.average_success_rate || 0}\n              precision={2}\n              suffix=\"%\"\n              prefix={<RobotOutlined />}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"验证率\"\n              value={stats.total_qa_pairs > 0 ?\n                ((stats.verified_qa_pairs / stats.total_qa_pairs) * 100) : 0}\n              precision={1}\n              suffix=\"%\"\n            />\n          </Col>\n        </Row>\n\n        <Space style={{ marginBottom: '16px' }}>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => {\n              setCreateModalVisible(true);\n              // 如果选择了连接，设置为默认值\n              if (selectedConnectionId) {\n                form.setFieldsValue({ connection_id: selectedConnectionId });\n              }\n            }}\n          >\n            创建问答对\n          </Button>\n          <Button\n            icon={<SearchOutlined />}\n            onClick={() => {\n              setSearchModalVisible(true);\n              // 如果选择了连接，设置为默认值\n              if (selectedConnectionId) {\n                searchForm.setFieldsValue({ connection_id: selectedConnectionId });\n              }\n            }}\n          >\n            智能搜索\n          </Button>\n          <Button icon={<ImportOutlined />}>\n            批量导入\n          </Button>\n          <Button icon={<ExportOutlined />}>\n            导出数据\n          </Button>\n        </Space>\n\n        <Tabs defaultActiveKey=\"list\">\n          <TabPane tab=\"问答对列表\" key=\"list\">\n            <Table\n              columns={[\n                {\n                  title: 'ID',\n                  dataIndex: 'id',\n                  key: 'id',\n                  width: 150,\n                  render: (text: string) => (\n                    <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>\n                      {text.substring(0, 12)}...\n                    </span>\n                  ),\n                },\n                {\n                  title: '问题',\n                  dataIndex: 'question',\n                  key: 'question',\n                  width: 300,\n                  ellipsis: true,\n                },\n                {\n                  title: 'SQL',\n                  dataIndex: 'sql',\n                  key: 'sql',\n                  width: 300,\n                  ellipsis: true,\n                  render: (text: string) => (\n                    <code style={{ fontSize: '12px', background: '#f5f5f5', padding: '2px 4px' }}>\n                      {text}\n                    </code>\n                  ),\n                },\n                {\n                  title: '连接ID',\n                  dataIndex: 'connection_id',\n                  key: 'connection_id',\n                  width: 80,\n                },\n                {\n                  title: '查询类型',\n                  dataIndex: 'query_type',\n                  key: 'query_type',\n                  width: 100,\n                  render: (type: string) => (\n                    <Tag color={getQueryTypeColor(type)}>{type}</Tag>\n                  ),\n                },\n                {\n                  title: '难度',\n                  dataIndex: 'difficulty_level',\n                  key: 'difficulty_level',\n                  width: 80,\n                  render: (level: number) => (\n                    <Tag color={getDifficultyColor(level)}>\n                      {level}\n                    </Tag>\n                  ),\n                },\n                {\n                  title: '成功率',\n                  dataIndex: 'success_rate',\n                  key: 'success_rate',\n                  width: 100,\n                  render: (rate: number) => `${(rate * 100).toFixed(1)}%`,\n                },\n                {\n                  title: '已验证',\n                  dataIndex: 'verified',\n                  key: 'verified',\n                  width: 80,\n                  render: (verified: boolean) => (\n                    <Tag color={verified ? 'green' : 'orange'}>\n                      {verified ? '是' : '否'}\n                    </Tag>\n                  ),\n                },\n                {\n                  title: '创建时间',\n                  dataIndex: 'created_at',\n                  key: 'created_at',\n                  width: 150,\n                  render: (date: string) => new Date(date).toLocaleString(),\n                },\n                {\n                  title: '操作',\n                  key: 'action',\n                  width: 120,\n                  render: (_: any, record: any) => (\n                    <Space size=\"small\">\n                      <Tooltip title=\"查看详情\">\n                        <Button\n                          type=\"text\"\n                          icon={<EyeOutlined />}\n                          onClick={() => handleViewDetail(record)}\n                        />\n                      </Tooltip>\n                      <Tooltip title=\"删除\">\n                        <Button\n                          type=\"text\"\n                          danger\n                          icon={<DeleteOutlined />}\n                          onClick={() => {\n                            // TODO: 实现删除功能\n                            message.info('删除功能开发中');\n                          }}\n                        />\n                      </Tooltip>\n                    </Space>\n                  ),\n                },\n              ]}\n              dataSource={qaPairs}\n              loading={loading}\n              rowKey=\"id\"\n              pagination={{\n                pageSize: 10,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total) => `共 ${total} 条记录`,\n              }}\n              scroll={{ x: 1400 }}\n            />\n          </TabPane>\n\n          <TabPane tab=\"智能搜索\" key=\"search\">\n            <Card size=\"small\" style={{ marginBottom: '16px' }}>\n              <Form\n                form={searchForm}\n                layout=\"inline\"\n                onFinish={handleSearchSimilar}\n              >\n                <Form.Item\n                  name=\"question\"\n                  rules={[{ required: true, message: '请输入问题' }]}\n                >\n                  <Input\n                    placeholder=\"输入自然语言问题\"\n                    style={{ width: 300 }}\n                  />\n                </Form.Item>\n                <Form.Item name=\"connection_id\">\n                  <Select\n                    placeholder=\"选择数据库连接\"\n                    style={{ width: 200 }}\n                    allowClear\n                  >\n                    {connections.map(conn => (\n                      <Option key={conn.id} value={conn.id}>\n                        {conn.name}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n                <Form.Item name=\"top_k\" initialValue={5}>\n                  <InputNumber\n                    placeholder=\"返回数量\"\n                    min={1}\n                    max={20}\n                    style={{ width: 100 }}\n                  />\n                </Form.Item>\n                <Form.Item>\n                  <Button\n                    type=\"primary\"\n                    htmlType=\"submit\"\n                    loading={loading}\n                    icon={<SearchOutlined />}\n                  >\n                    搜索\n                  </Button>\n                </Form.Item>\n              </Form>\n            </Card>\n\n            <Table\n              columns={searchColumns}\n              dataSource={searchResults}\n              loading={loading}\n              rowKey={(record) => record.qa_pair.id}\n              pagination={{\n                pageSize: 10,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total) => `共 ${total} 条记录`,\n              }}\n              scroll={{ x: 1200 }}\n            />\n          </TabPane>\n\n          <TabPane tab=\"统计分析\" key=\"stats\">\n            <Row gutter={16}>\n              <Col span={12}>\n                <Card title=\"查询类型分布\" size=\"small\">\n                  {stats.query_types && Object.entries(stats.query_types).map(([type, count]: [string, any]) => (\n                    <div key={type} style={{ marginBottom: '8px' }}>\n                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <Tag color={getQueryTypeColor(type)} style={{ fontSize: '12px' }}>{type}</Tag>\n                        <span>{count}</span>\n                      </div>\n                      <Progress\n                        percent={stats.total_qa_pairs > 0 ? (count / stats.total_qa_pairs) * 100 : 0}\n                        size=\"small\"\n                        showInfo={false}\n                      />\n                    </div>\n                  ))}\n                </Card>\n              </Col>\n              <Col span={12}>\n                <Card title=\"难度分布\" size=\"small\">\n                  {stats.difficulty_distribution && Object.entries(stats.difficulty_distribution).map(([level, count]: [string, any]) => (\n                    <div key={level} style={{ marginBottom: '8px' }}>\n                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <span>难度 {level}</span>\n                        <span>{count}</span>\n                      </div>\n                      <Progress\n                        percent={stats.total_qa_pairs > 0 ? (count / stats.total_qa_pairs) * 100 : 0}\n                        size=\"small\"\n                        showInfo={false}\n                        strokeColor={getDifficultyColor(parseInt(level))}\n                      />\n                    </div>\n                  ))}\n                </Card>\n              </Col>\n            </Row>\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 创建问答对模态框 */}\n      <Modal\n        title=\"创建问答对\"\n        open={createModalVisible}\n        onCancel={() => setCreateModalVisible(false)}\n        footer={null}\n        width={800}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleCreateQAPair}\n        >\n          <Form.Item\n            name=\"question\"\n            label=\"自然语言问题\"\n            rules={[{ required: true, message: '请输入问题' }]}\n          >\n            <TextArea rows={3} placeholder=\"输入自然语言问题\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"sql\"\n            label=\"SQL语句\"\n            rules={[{ required: true, message: '请输入SQL语句' }]}\n          >\n            <TextArea rows={5} placeholder=\"输入对应的SQL语句\" />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"connection_id\"\n                label=\"数据库连接\"\n                rules={[{ required: true, message: '请选择数据库连接' }]}\n              >\n                <Select\n                  style={{ width: '100%' }}\n                  placeholder=\"选择数据库连接\"\n                  loading={loadingConnections}\n                >\n                  {connections.map(conn => (\n                    <Option key={conn.id} value={conn.id}>\n                      {conn.name} ({conn.db_type})\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"query_type\"\n                label=\"查询类型\"\n                initialValue=\"SELECT\"\n              >\n                <Select>\n                  <Option value=\"SELECT\">SELECT</Option>\n                  <Option value=\"JOIN\">JOIN</Option>\n                  <Option value=\"AGGREGATE\">AGGREGATE</Option>\n                  <Option value=\"GROUP_BY\">GROUP_BY</Option>\n                  <Option value=\"ORDER_BY\">ORDER_BY</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"difficulty_level\"\n                label=\"难度等级\"\n                initialValue={3}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"verified\"\n            label=\"已验证\"\n            valuePropName=\"checked\"\n            initialValue={false}\n          >\n            <Switch />\n          </Form.Item>\n\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n                创建\n              </Button>\n              <Button onClick={() => setCreateModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 详情模态框 */}\n      <Modal\n        title=\"问答对详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedQAPair && (\n          <div>\n            <Divider orientation=\"left\">基本信息</Divider>\n            <Row gutter={16}>\n              <Col span={12}>\n                <p><strong>ID:</strong> {selectedQAPair.id}</p>\n                <p><strong>查询类型:</strong> <Tag color={getQueryTypeColor(selectedQAPair.query_type)}>{selectedQAPair.query_type}</Tag></p>\n                <p><strong>难度等级:</strong> {selectedQAPair.difficulty_level}</p>\n                <p><strong>数据库连接:</strong> {(() => {\n                  const connection = connections.find(conn => conn.id === selectedQAPair.connection_id);\n                  return connection ? (\n                    <Tag color=\"blue\">{connection.name} ({connection.db_type})</Tag>\n                  ) : (\n                    <Tag color=\"default\">ID: {selectedQAPair.connection_id}</Tag>\n                  );\n                })()}</p>\n              </Col>\n              <Col span={12}>\n                <p><strong>成功率:</strong> {(selectedQAPair.success_rate * 100).toFixed(1)}%</p>\n                <p><strong>已验证:</strong> {selectedQAPair.verified ? '是' : '否'}</p>\n                <p><strong>创建时间:</strong> {new Date(selectedQAPair.created_at).toLocaleString()}</p>\n              </Col>\n            </Row>\n\n            <Divider orientation=\"left\">问题</Divider>\n            <p style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>\n              {selectedQAPair.question}\n            </p>\n\n            <Divider orientation=\"left\">SQL语句</Divider>\n            <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px', overflow: 'auto' }}>\n              {selectedQAPair.sql}\n            </pre>\n\n            <Divider orientation=\"left\">使用的表</Divider>\n            <div>\n              {selectedQAPair.used_tables?.map((table, index) => (\n                <Tag key={index} color=\"blue\">{table}</Tag>\n              ))}\n            </div>\n\n            <Divider orientation=\"left\">提及的实体</Divider>\n            <div>\n              {selectedQAPair.mentioned_entities?.map((entity, index) => (\n                <Tag key={index} color=\"green\">{entity}</Tag>\n              ))}\n            </div>\n          </div>\n        )}\n      </Modal>\n\n      {/* 反馈模态框 */}\n      <QAFeedbackModal\n        visible={feedbackModalVisible}\n        onCancel={() => setFeedbackModalVisible(false)}\n        qaPair={selectedQAPair}\n        onFeedbackSubmitted={() => {\n          // 重新加载统计信息\n          loadStats(selectedConnectionId || undefined);\n        }}\n      />\n    </div>\n  );\n};\n\nexport default HybridQAPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,OAAO,QACF,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,cAAc,EAEdC,WAAW,EACXC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,sBAAsB,QACjB,mBAAmB;AAC1B,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,eAAe,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAI/D,MAAM;EAAEC;AAAQ,CAAC,GAAGtB,IAAI;AACxB,MAAM;EAAEuB;AAAS,CAAC,GAAG5B,KAAK;AAC1B,MAAM;EAAE6B;AAAO,CAAC,GAAG5B,MAAM;AAEzB,MAAM6B,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACnC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACiD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAkB,EAAE,CAAC;EACvE,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAM,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC6D,WAAW,EAAEC,cAAc,CAAC,GAAG9D,QAAQ,CAAiB,EAAE,CAAC;EAClE,MAAM,CAAC+D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhE,QAAQ,CAAgB,IAAI,CAAC;EACrF,MAAM,CAACiE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACmE,IAAI,CAAC,GAAG3D,IAAI,CAAC4D,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAG7D,IAAI,CAAC4D,OAAO,CAAC,CAAC;EAEnCnE,SAAS,CAAC,MAAM;IACdqE,eAAe,CAAC,CAAC;IACjBC,SAAS,CAAC,CAAC;IACXC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAENvE,SAAS,CAAC,MAAM;IACd,IAAI8D,oBAAoB,EAAE;MACxBQ,SAAS,CAACR,oBAAoB,CAAC;MAC/BS,WAAW,CAACT,oBAAoB,CAAC;IACnC;EACF,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAE1B,MAAMO,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFJ,qBAAqB,CAAC,IAAI,CAAC;MAC3B,MAAMO,QAAQ,GAAG,MAAMzC,cAAc,CAAC,CAAC;MACvC8B,cAAc,CAACW,QAAQ,CAACC,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd9D,OAAO,CAAC8D,KAAK,CAAC,WAAW,CAAC;MAC1BC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRT,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMM,WAAW,GAAG,MAAOK,YAAqB,IAAK;IACnD,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM2B,QAAQ,GAAG,MAAM1C,eAAe,CAAC+C,UAAU,CAACD,YAAY,CAAC;MAC/DjC,UAAU,CAAC6B,QAAQ,CAAC;IACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC9D,OAAO,CAAC8D,KAAK,CAAC,WAAW,CAAC;IAC5B,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,SAAS,GAAG,MAAOM,YAAqB,IAAK;IACjD,IAAI;MACF,MAAMJ,QAAQ,GAAG,MAAM1C,eAAe,CAACgD,QAAQ,CAACF,YAAY,CAAC;MAC7DjB,QAAQ,CAACa,QAAQ,CAAC;IACpB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAOC,MAAoB,IAAK;IACzD,IAAI;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMf,eAAe,CAACmD,YAAY,CAACD,MAAM,CAAC;MAC1CpE,OAAO,CAACsE,OAAO,CAAC,SAAS,CAAC;MAC1BnC,qBAAqB,CAAC,KAAK,CAAC;MAC5BmB,IAAI,CAACiB,WAAW,CAAC,CAAC;MAClBb,SAAS,CAACR,oBAAoB,IAAIsB,SAAS,CAAC,CAAC,CAAC;MAC9Cb,WAAW,CAACT,oBAAoB,IAAIsB,SAAS,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd9D,OAAO,CAAC8D,KAAK,CAAC,MAAM,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwC,mBAAmB,GAAG,MAAOL,MAAW,IAAK;IACjD,IAAI;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyC,OAAO,GAAG,MAAMxD,eAAe,CAACyD,aAAa,CAAC;QAClDC,QAAQ,EAAER,MAAM,CAACQ,QAAQ;QACzBC,aAAa,EAAET,MAAM,CAACS,aAAa,IAAI3B,oBAAoB;QAC3D4B,KAAK,EAAEV,MAAM,CAACU,KAAK,IAAI;MACzB,CAAC,CAAC;MACFjC,gBAAgB,CAAC6B,OAAO,CAAC;IAC3B,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd9D,OAAO,CAAC8D,KAAK,CAAC,MAAM,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IACpC,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8C,gBAAgB,GAAIC,MAAc,IAAK;IAC3CrC,iBAAiB,CAACqC,MAAM,CAAC;IACzBzC,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM0C,iBAAiB,GAAIC,IAAY,IAAK;IAC1C,MAAMC,MAA8B,GAAG;MACrC,QAAQ,EAAE,MAAM;MAChB,MAAM,EAAE,OAAO;MACf,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE;IACd,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;EAED,MAAME,kBAAkB,GAAIC,KAAa,IAAK;IAC5C,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,OAAO;IAC9B,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC/B,OAAO,KAAK;EACd,CAAC;EAED,MAAMC,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;IAClCC,GAAG,EAAE,UAAU;IACfC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC;IAC7BC,GAAG,EAAE,KAAK;IACVC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,GAAW,iBAClBvE,OAAA;MAAMwE,KAAK,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAU,CAAE;MAAAC,QAAA,EAC1EL;IAAG;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAEV,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;IACpCC,GAAG,EAAE,YAAY;IACjBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGV,IAAY,iBACnB5D,OAAA,CAAC7B,GAAG;MAAC8G,KAAK,EAAEtB,iBAAiB,CAACC,IAAI,CAAE;MAAAgB,QAAA,EAAEhB;IAAI;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEpD,CAAC,EACD;IACEf,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;IACvCC,GAAG,EAAE,eAAe;IACpBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG5B,YAAoB,IAAK;MAChC,MAAMwC,UAAU,GAAGxD,WAAW,CAACyD,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAK3C,YAAY,CAAC;MACrE,OAAOwC,UAAU,gBACflF,OAAA,CAAChB,OAAO;QAACiF,KAAK,EAAE,GAAGiB,UAAU,CAACI,OAAO,MAAMJ,UAAU,CAACK,aAAa,EAAG;QAAAX,QAAA,eACpE5E,OAAA,CAAC7B,GAAG;UAAC8G,KAAK,EAAC,MAAM;UAAAL,QAAA,EAAEM,UAAU,CAACM;QAAI;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,gBAEVhF,OAAA,CAAC7B,GAAG;QAAC8G,KAAK,EAAC,SAAS;QAAAL,QAAA,GAAC,MAAI,EAAClC,YAAY;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAC7C;IACH;EACF,CAAC,EACD;IACEf,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGmB,KAAa,iBACpBzF,OAAA,CAACjB,QAAQ;MACP2G,OAAO,EAAEC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAG,GAAG,CAAE;MACjCI,IAAI,EAAC,OAAO;MACZC,MAAM,EAAEL,KAAK,GAAG,GAAG,GAAG,SAAS,GAAGA,KAAK,GAAG,GAAG,GAAG,QAAQ,GAAG;IAAY;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE;EAEL,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbE,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACyB,CAAM,EAAEC,MAAqB,kBACpChG,OAAA,CAAC9B,KAAK;MAAC2H,IAAI,EAAC,OAAO;MAAAjB,QAAA,gBACjB5E,OAAA,CAAChB,OAAO;QAACiF,KAAK,EAAC,0BAAM;QAAAW,QAAA,eACnB5E,OAAA,CAAC/B,MAAM;UACL2F,IAAI,EAAC,MAAM;UACXqC,IAAI,eAAEjG,OAAA,CAACT,WAAW;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBkB,OAAO,EAAEA,CAAA,KAAMzC,gBAAgB,CAACuC,MAAM,CAACG,OAAO;QAAE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVhF,OAAA,CAAC/B,MAAM;QACL2F,IAAI,EAAC,MAAM;QACXiC,IAAI,EAAC,OAAO;QACZK,OAAO,EAAEA,CAAA,KAAM;UACb7E,iBAAiB,CAAC2E,MAAM,CAACG,OAAO,CAAC;UACjChF,uBAAuB,CAAC,IAAI,CAAC;QAC/B,CAAE;QAAAyD,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;EAED,oBACEhF,OAAA;IAAKwE,KAAK,EAAE;MAAEG,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9B5E,OAAA,CAACjC,IAAI;MAACkG,KAAK,EAAC,wDAAW;MAACO,KAAK,EAAE;QAAE4B,YAAY,EAAE;MAAO,CAAE;MAAAxB,QAAA,gBAEtD5E,OAAA;QAAKwE,KAAK,EAAE;UAAE4B,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA3B,QAAA,gBAC3G5E,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAMwE,KAAK,EAAE;cAAEgC,WAAW,EAAE;YAAM,CAAE;YAAA5B,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDhF,OAAA,CAACzB,MAAM;YACLkI,WAAW,EAAC,oEAAa;YACzBC,KAAK,EAAE9E,oBAAqB;YAC5B+E,QAAQ,EAAE9E,uBAAwB;YAClCnB,OAAO,EAAEoB,kBAAmB;YAC5B0C,KAAK,EAAE;cAAEH,KAAK,EAAE;YAAI,CAAE;YACtBuC,UAAU;YAAAhC,QAAA,EAETlD,WAAW,CAACmF,GAAG,CAACzB,IAAI,iBACnBpF,OAAA,CAACG,MAAM;cAAeuG,KAAK,EAAEtB,IAAI,CAACC,EAAG;cAAAT,QAAA,GAClCQ,IAAI,CAACI,IAAI,EAAC,IAAE,EAACJ,IAAI,CAACE,OAAO,EAAC,KAAG,EAACF,IAAI,CAACG,aAAa,EAAC,GACpD;YAAA,GAFaH,IAAI,CAACC,EAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNhF,OAAA,CAAChB,OAAO;UAACiF,KAAK,EAAC,sLAAgC;UAAAW,QAAA,eAC7C5E,OAAA,CAACL,sBAAsB;YAAC6E,KAAK,EAAE;cAAES,KAAK,EAAE;YAAO;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAENhF,OAAA,CAACpB,GAAG;QAACkI,MAAM,EAAE,EAAG;QAACtC,KAAK,EAAE;UAAE4B,YAAY,EAAE;QAAO,CAAE;QAAAxB,QAAA,gBAC/C5E,OAAA,CAACnB,GAAG;UAACkI,IAAI,EAAE,CAAE;UAAAnC,QAAA,eACX5E,OAAA,CAAClB,SAAS;YACRmF,KAAK,EAAC,gCAAO;YACbyC,KAAK,EAAElF,KAAK,CAACwF,cAAc,IAAI,CAAE;YACjCC,MAAM,eAAEjH,OAAA,CAACP,gBAAgB;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhF,OAAA,CAACnB,GAAG;UAACkI,IAAI,EAAE,CAAE;UAAAnC,QAAA,eACX5E,OAAA,CAAClB,SAAS;YACRmF,KAAK,EAAC,0BAAM;YACZyC,KAAK,EAAElF,KAAK,CAAC0F,iBAAiB,IAAI,CAAE;YACpCD,MAAM,eAAEjH,OAAA,CAACN,YAAY;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhF,OAAA,CAACnB,GAAG;UAACkI,IAAI,EAAE,CAAE;UAAAnC,QAAA,eACX5E,OAAA,CAAClB,SAAS;YACRmF,KAAK,EAAC,gCAAO;YACbyC,KAAK,EAAElF,KAAK,CAAC2F,oBAAoB,IAAI,CAAE;YACvCC,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC,GAAG;YACVJ,MAAM,eAAEjH,OAAA,CAACR,aAAa;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhF,OAAA,CAACnB,GAAG;UAACkI,IAAI,EAAE,CAAE;UAAAnC,QAAA,eACX5E,OAAA,CAAClB,SAAS;YACRmF,KAAK,EAAC,oBAAK;YACXyC,KAAK,EAAElF,KAAK,CAACwF,cAAc,GAAG,CAAC,GAC3BxF,KAAK,CAAC0F,iBAAiB,GAAG1F,KAAK,CAACwF,cAAc,GAAI,GAAG,GAAI,CAAE;YAC/DI,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC;UAAG;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA,CAAC9B,KAAK;QAACsG,KAAK,EAAE;UAAE4B,YAAY,EAAE;QAAO,CAAE;QAAAxB,QAAA,gBACrC5E,OAAA,CAAC/B,MAAM;UACL2F,IAAI,EAAC,SAAS;UACdqC,IAAI,eAAEjG,OAAA,CAACd,YAAY;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBkB,OAAO,EAAEA,CAAA,KAAM;YACbrF,qBAAqB,CAAC,IAAI,CAAC;YAC3B;YACA,IAAIe,oBAAoB,EAAE;cACxBI,IAAI,CAACsF,cAAc,CAAC;gBAAE/D,aAAa,EAAE3B;cAAqB,CAAC,CAAC;YAC9D;UACF,CAAE;UAAAgD,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThF,OAAA,CAAC/B,MAAM;UACLgI,IAAI,eAAEjG,OAAA,CAACb,cAAc;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBkB,OAAO,EAAEA,CAAA,KAAM;YACbnF,qBAAqB,CAAC,IAAI,CAAC;YAC3B;YACA,IAAIa,oBAAoB,EAAE;cACxBM,UAAU,CAACoF,cAAc,CAAC;gBAAE/D,aAAa,EAAE3B;cAAqB,CAAC,CAAC;YACpE;UACF,CAAE;UAAAgD,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThF,OAAA,CAAC/B,MAAM;UAACgI,IAAI,eAAEjG,OAAA,CAACX,cAAc;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThF,OAAA,CAAC/B,MAAM;UAACgI,IAAI,eAAEjG,OAAA,CAACZ,cAAc;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAERhF,OAAA,CAACrB,IAAI;QAAC4I,gBAAgB,EAAC,MAAM;QAAA3C,QAAA,gBAC3B5E,OAAA,CAACC,OAAO;UAACuH,GAAG,EAAC,gCAAO;UAAA5C,QAAA,eAClB5E,OAAA,CAAChC,KAAK;YACJyJ,OAAO,EAAE,CACP;cACExD,KAAK,EAAE,IAAI;cACXC,SAAS,EAAE,IAAI;cACfC,GAAG,EAAE,IAAI;cACTE,KAAK,EAAE,GAAG;cACVC,MAAM,EAAGoD,IAAY,iBACnB1H,OAAA;gBAAMwE,KAAK,EAAE;kBAAEmD,UAAU,EAAE,WAAW;kBAAElD,QAAQ,EAAE;gBAAO,CAAE;gBAAAG,QAAA,GACxD8C,IAAI,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KACzB;cAAA;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAEV,CAAC,EACD;cACEf,KAAK,EAAE,IAAI;cACXC,SAAS,EAAE,UAAU;cACrBC,GAAG,EAAE,UAAU;cACfE,KAAK,EAAE,GAAG;cACVD,QAAQ,EAAE;YACZ,CAAC,EACD;cACEH,KAAK,EAAE,KAAK;cACZC,SAAS,EAAE,KAAK;cAChBC,GAAG,EAAE,KAAK;cACVE,KAAK,EAAE,GAAG;cACVD,QAAQ,EAAE,IAAI;cACdE,MAAM,EAAGoD,IAAY,iBACnB1H,OAAA;gBAAMwE,KAAK,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,SAAS;kBAAEC,OAAO,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EAC1E8C;cAAI;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAEV,CAAC,EACD;cACEf,KAAK,EAAE,MAAM;cACbC,SAAS,EAAE,eAAe;cAC1BC,GAAG,EAAE,eAAe;cACpBE,KAAK,EAAE;YACT,CAAC,EACD;cACEJ,KAAK,EAAE,MAAM;cACbC,SAAS,EAAE,YAAY;cACvBC,GAAG,EAAE,YAAY;cACjBE,KAAK,EAAE,GAAG;cACVC,MAAM,EAAGV,IAAY,iBACnB5D,OAAA,CAAC7B,GAAG;gBAAC8G,KAAK,EAAEtB,iBAAiB,CAACC,IAAI,CAAE;gBAAAgB,QAAA,EAAEhB;cAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAEpD,CAAC,EACD;cACEf,KAAK,EAAE,IAAI;cACXC,SAAS,EAAE,kBAAkB;cAC7BC,GAAG,EAAE,kBAAkB;cACvBE,KAAK,EAAE,EAAE;cACTC,MAAM,EAAGP,KAAa,iBACpB/D,OAAA,CAAC7B,GAAG;gBAAC8G,KAAK,EAAEnB,kBAAkB,CAACC,KAAK,CAAE;gBAAAa,QAAA,EACnCb;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAET,CAAC,EACD;cACEf,KAAK,EAAE,KAAK;cACZC,SAAS,EAAE,cAAc;cACzBC,GAAG,EAAE,cAAc;cACnBE,KAAK,EAAE,GAAG;cACVC,MAAM,EAAGuD,IAAY,IAAK,GAAG,CAACA,IAAI,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;YACtD,CAAC,EACD;cACE7D,KAAK,EAAE,KAAK;cACZC,SAAS,EAAE,UAAU;cACrBC,GAAG,EAAE,UAAU;cACfE,KAAK,EAAE,EAAE;cACTC,MAAM,EAAGyD,QAAiB,iBACxB/H,OAAA,CAAC7B,GAAG;gBAAC8G,KAAK,EAAE8C,QAAQ,GAAG,OAAO,GAAG,QAAS;gBAAAnD,QAAA,EACvCmD,QAAQ,GAAG,GAAG,GAAG;cAAG;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAET,CAAC,EACD;cACEf,KAAK,EAAE,MAAM;cACbC,SAAS,EAAE,YAAY;cACvBC,GAAG,EAAE,YAAY;cACjBE,KAAK,EAAE,GAAG;cACVC,MAAM,EAAG0D,IAAY,IAAK,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC;YAC1D,CAAC,EACD;cACEjE,KAAK,EAAE,IAAI;cACXE,GAAG,EAAE,QAAQ;cACbE,KAAK,EAAE,GAAG;cACVC,MAAM,EAAEA,CAACyB,CAAM,EAAEC,MAAW,kBAC1BhG,OAAA,CAAC9B,KAAK;gBAAC2H,IAAI,EAAC,OAAO;gBAAAjB,QAAA,gBACjB5E,OAAA,CAAChB,OAAO;kBAACiF,KAAK,EAAC,0BAAM;kBAAAW,QAAA,eACnB5E,OAAA,CAAC/B,MAAM;oBACL2F,IAAI,EAAC,MAAM;oBACXqC,IAAI,eAAEjG,OAAA,CAACT,WAAW;sBAAAsF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtBkB,OAAO,EAAEA,CAAA,KAAMzC,gBAAgB,CAACuC,MAAM;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACVhF,OAAA,CAAChB,OAAO;kBAACiF,KAAK,EAAC,cAAI;kBAAAW,QAAA,eACjB5E,OAAA,CAAC/B,MAAM;oBACL2F,IAAI,EAAC,MAAM;oBACXuE,MAAM;oBACNlC,IAAI,eAAEjG,OAAA,CAACV,cAAc;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzBkB,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACAxH,OAAO,CAAC0J,IAAI,CAAC,SAAS,CAAC;oBACzB;kBAAE;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAEX,CAAC,CACD;YACFqD,UAAU,EAAE7H,OAAQ;YACpBE,OAAO,EAAEA,OAAQ;YACjB4H,MAAM,EAAC,IAAI;YACXC,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC,CAAE;YACFC,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAK;UAAE;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC,GA1HqB,MAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2HtB,CAAC,eAEVhF,OAAA,CAACC,OAAO;UAACuH,GAAG,EAAC,0BAAM;UAAA5C,QAAA,gBACjB5E,OAAA,CAACjC,IAAI;YAAC8H,IAAI,EAAC,OAAO;YAACrB,KAAK,EAAE;cAAE4B,YAAY,EAAE;YAAO,CAAE;YAAAxB,QAAA,eACjD5E,OAAA,CAAC3B,IAAI;cACH2D,IAAI,EAAEE,UAAW;cACjB6G,MAAM,EAAC,QAAQ;cACfC,QAAQ,EAAE7F,mBAAoB;cAAAyB,QAAA,gBAE9B5E,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;gBACRzD,IAAI,EAAC,UAAU;gBACf0D,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAEzK,OAAO,EAAE;gBAAQ,CAAC,CAAE;gBAAAkG,QAAA,eAE9C5E,OAAA,CAAC1B,KAAK;kBACJmI,WAAW,EAAC,kDAAU;kBACtBjC,KAAK,EAAE;oBAAEH,KAAK,EAAE;kBAAI;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZhF,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;gBAACzD,IAAI,EAAC,eAAe;gBAAAZ,QAAA,eAC7B5E,OAAA,CAACzB,MAAM;kBACLkI,WAAW,EAAC,4CAAS;kBACrBjC,KAAK,EAAE;oBAAEH,KAAK,EAAE;kBAAI,CAAE;kBACtBuC,UAAU;kBAAAhC,QAAA,EAETlD,WAAW,CAACmF,GAAG,CAACzB,IAAI,iBACnBpF,OAAA,CAACG,MAAM;oBAAeuG,KAAK,EAAEtB,IAAI,CAACC,EAAG;oBAAAT,QAAA,EAClCQ,IAAI,CAACI;kBAAI,GADCJ,IAAI,CAACC,EAAE;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEZ,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACZhF,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;gBAACzD,IAAI,EAAC,OAAO;gBAAC4D,YAAY,EAAE,CAAE;gBAAAxE,QAAA,eACtC5E,OAAA,CAACxB,WAAW;kBACViI,WAAW,EAAC,0BAAM;kBAClB4C,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,EAAG;kBACR9E,KAAK,EAAE;oBAAEH,KAAK,EAAE;kBAAI;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZhF,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;gBAAArE,QAAA,eACR5E,OAAA,CAAC/B,MAAM;kBACL2F,IAAI,EAAC,SAAS;kBACd2F,QAAQ,EAAC,QAAQ;kBACjB7I,OAAO,EAAEA,OAAQ;kBACjBuF,IAAI,eAAEjG,OAAA,CAACb,cAAc;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,EAC1B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPhF,OAAA,CAAChC,KAAK;YACJyJ,OAAO,EAAEzD,aAAc;YACvBqE,UAAU,EAAE/G,aAAc;YAC1BZ,OAAO,EAAEA,OAAQ;YACjB4H,MAAM,EAAGtC,MAAM,IAAKA,MAAM,CAACG,OAAO,CAACd,EAAG;YACtCkD,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC,CAAE;YACFC,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAK;UAAE;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA,GA9DoB,QAAQ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+DvB,CAAC,eAEVhF,OAAA,CAACC,OAAO;UAACuH,GAAG,EAAC,0BAAM;UAAA5C,QAAA,eACjB5E,OAAA,CAACpB,GAAG;YAACkI,MAAM,EAAE,EAAG;YAAAlC,QAAA,gBACd5E,OAAA,CAACnB,GAAG;cAACkI,IAAI,EAAE,EAAG;cAAAnC,QAAA,eACZ5E,OAAA,CAACjC,IAAI;gBAACkG,KAAK,EAAC,sCAAQ;gBAAC4B,IAAI,EAAC,OAAO;gBAAAjB,QAAA,EAC9BpD,KAAK,CAACgI,WAAW,IAAIC,MAAM,CAACC,OAAO,CAAClI,KAAK,CAACgI,WAAW,CAAC,CAAC3C,GAAG,CAAC,CAAC,CAACjD,IAAI,EAAE+F,KAAK,CAAgB,kBACvF3J,OAAA;kBAAgBwE,KAAK,EAAE;oBAAE4B,YAAY,EAAE;kBAAM,CAAE;kBAAAxB,QAAA,gBAC7C5E,OAAA;oBAAKwE,KAAK,EAAE;sBAAE6B,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEC,UAAU,EAAE;oBAAS,CAAE;oBAAA3B,QAAA,gBACrF5E,OAAA,CAAC7B,GAAG;sBAAC8G,KAAK,EAAEtB,iBAAiB,CAACC,IAAI,CAAE;sBAACY,KAAK,EAAE;wBAAEC,QAAQ,EAAE;sBAAO,CAAE;sBAAAG,QAAA,EAAEhB;oBAAI;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9EhF,OAAA;sBAAA4E,QAAA,EAAO+E;oBAAK;sBAAA9E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACNhF,OAAA,CAACjB,QAAQ;oBACP2G,OAAO,EAAElE,KAAK,CAACwF,cAAc,GAAG,CAAC,GAAI2C,KAAK,GAAGnI,KAAK,CAACwF,cAAc,GAAI,GAAG,GAAG,CAAE;oBAC7EnB,IAAI,EAAC,OAAO;oBACZ+D,QAAQ,EAAE;kBAAM;oBAAA/E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA,GATMpB,IAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUT,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhF,OAAA,CAACnB,GAAG;cAACkI,IAAI,EAAE,EAAG;cAAAnC,QAAA,eACZ5E,OAAA,CAACjC,IAAI;gBAACkG,KAAK,EAAC,0BAAM;gBAAC4B,IAAI,EAAC,OAAO;gBAAAjB,QAAA,EAC5BpD,KAAK,CAACqI,uBAAuB,IAAIJ,MAAM,CAACC,OAAO,CAAClI,KAAK,CAACqI,uBAAuB,CAAC,CAAChD,GAAG,CAAC,CAAC,CAAC9C,KAAK,EAAE4F,KAAK,CAAgB,kBAChH3J,OAAA;kBAAiBwE,KAAK,EAAE;oBAAE4B,YAAY,EAAE;kBAAM,CAAE;kBAAAxB,QAAA,gBAC9C5E,OAAA;oBAAKwE,KAAK,EAAE;sBAAE6B,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEC,UAAU,EAAE;oBAAS,CAAE;oBAAA3B,QAAA,gBACrF5E,OAAA;sBAAA4E,QAAA,GAAM,eAAG,EAACb,KAAK;oBAAA;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACvBhF,OAAA;sBAAA4E,QAAA,EAAO+E;oBAAK;sBAAA9E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACNhF,OAAA,CAACjB,QAAQ;oBACP2G,OAAO,EAAElE,KAAK,CAACwF,cAAc,GAAG,CAAC,GAAI2C,KAAK,GAAGnI,KAAK,CAACwF,cAAc,GAAI,GAAG,GAAG,CAAE;oBAC7EnB,IAAI,EAAC,OAAO;oBACZ+D,QAAQ,EAAE,KAAM;oBAChBE,WAAW,EAAEhG,kBAAkB,CAACiG,QAAQ,CAAChG,KAAK,CAAC;kBAAE;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA,GAVMjB,KAAK;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GArCgB,OAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsCtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPhF,OAAA,CAAC5B,KAAK;MACJ6F,KAAK,EAAC,gCAAO;MACb+F,IAAI,EAAEpJ,kBAAmB;MACzBqJ,QAAQ,EAAEA,CAAA,KAAMpJ,qBAAqB,CAAC,KAAK,CAAE;MAC7CqJ,MAAM,EAAE,IAAK;MACb7F,KAAK,EAAE,GAAI;MAAAO,QAAA,eAEX5E,OAAA,CAAC3B,IAAI;QACH2D,IAAI,EAAEA,IAAK;QACX+G,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEnG,kBAAmB;QAAA+B,QAAA,gBAE7B5E,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;UACRzD,IAAI,EAAC,UAAU;UACf2E,KAAK,EAAC,sCAAQ;UACdjB,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzK,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAkG,QAAA,eAE9C5E,OAAA,CAACE,QAAQ;YAACkK,IAAI,EAAE,CAAE;YAAC3D,WAAW,EAAC;UAAU;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAEZhF,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;UACRzD,IAAI,EAAC,KAAK;UACV2E,KAAK,EAAC,iBAAO;UACbjB,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzK,OAAO,EAAE;UAAW,CAAC,CAAE;UAAAkG,QAAA,eAEjD5E,OAAA,CAACE,QAAQ;YAACkK,IAAI,EAAE,CAAE;YAAC3D,WAAW,EAAC;UAAY;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAEZhF,OAAA,CAACpB,GAAG;UAACkI,MAAM,EAAE,EAAG;UAAAlC,QAAA,gBACd5E,OAAA,CAACnB,GAAG;YAACkI,IAAI,EAAE,CAAE;YAAAnC,QAAA,eACX5E,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;cACRzD,IAAI,EAAC,eAAe;cACpB2E,KAAK,EAAC,gCAAO;cACbjB,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzK,OAAO,EAAE;cAAW,CAAC,CAAE;cAAAkG,QAAA,eAEjD5E,OAAA,CAACzB,MAAM;gBACLiG,KAAK,EAAE;kBAAEH,KAAK,EAAE;gBAAO,CAAE;gBACzBoC,WAAW,EAAC,4CAAS;gBACrB/F,OAAO,EAAEoB,kBAAmB;gBAAA8C,QAAA,EAE3BlD,WAAW,CAACmF,GAAG,CAACzB,IAAI,iBACnBpF,OAAA,CAACG,MAAM;kBAAeuG,KAAK,EAAEtB,IAAI,CAACC,EAAG;kBAAAT,QAAA,GAClCQ,IAAI,CAACI,IAAI,EAAC,IAAE,EAACJ,IAAI,CAACE,OAAO,EAAC,GAC7B;gBAAA,GAFaF,IAAI,CAACC,EAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEZ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNhF,OAAA,CAACnB,GAAG;YAACkI,IAAI,EAAE,CAAE;YAAAnC,QAAA,eACX5E,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;cACRzD,IAAI,EAAC,YAAY;cACjB2E,KAAK,EAAC,0BAAM;cACZf,YAAY,EAAC,QAAQ;cAAAxE,QAAA,eAErB5E,OAAA,CAACzB,MAAM;gBAAAqG,QAAA,gBACL5E,OAAA,CAACG,MAAM;kBAACuG,KAAK,EAAC,QAAQ;kBAAA9B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtChF,OAAA,CAACG,MAAM;kBAACuG,KAAK,EAAC,MAAM;kBAAA9B,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClChF,OAAA,CAACG,MAAM;kBAACuG,KAAK,EAAC,WAAW;kBAAA9B,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5ChF,OAAA,CAACG,MAAM;kBAACuG,KAAK,EAAC,UAAU;kBAAA9B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1ChF,OAAA,CAACG,MAAM;kBAACuG,KAAK,EAAC,UAAU;kBAAA9B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNhF,OAAA,CAACnB,GAAG;YAACkI,IAAI,EAAE,CAAE;YAAAnC,QAAA,eACX5E,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;cACRzD,IAAI,EAAC,kBAAkB;cACvB2E,KAAK,EAAC,0BAAM;cACZf,YAAY,EAAE,CAAE;cAAAxE,QAAA,eAEhB5E,OAAA,CAACxB,WAAW;gBAAC6K,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAC9E,KAAK,EAAE;kBAAEH,KAAK,EAAE;gBAAO;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhF,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;UACRzD,IAAI,EAAC,UAAU;UACf2E,KAAK,EAAC,oBAAK;UACXE,aAAa,EAAC,SAAS;UACvBjB,YAAY,EAAE,KAAM;UAAAxE,QAAA,eAEpB5E,OAAA,CAACvB,MAAM;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEZhF,OAAA,CAAC3B,IAAI,CAAC4K,IAAI;UAAArE,QAAA,eACR5E,OAAA,CAAC9B,KAAK;YAAA0G,QAAA,gBACJ5E,OAAA,CAAC/B,MAAM;cAAC2F,IAAI,EAAC,SAAS;cAAC2F,QAAQ,EAAC,QAAQ;cAAC7I,OAAO,EAAEA,OAAQ;cAAAkE,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThF,OAAA,CAAC/B,MAAM;cAACiI,OAAO,EAAEA,CAAA,KAAMrF,qBAAqB,CAAC,KAAK,CAAE;cAAA+D,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRhF,OAAA,CAAC5B,KAAK;MACJ6F,KAAK,EAAC,gCAAO;MACb+F,IAAI,EAAEhJ,kBAAmB;MACzBiJ,QAAQ,EAAEA,CAAA,KAAMhJ,qBAAqB,CAAC,KAAK,CAAE;MAC7CiJ,MAAM,EAAE,cACNlK,OAAA,CAAC/B,MAAM;QAAaiI,OAAO,EAAEA,CAAA,KAAMjF,qBAAqB,CAAC,KAAK,CAAE;QAAA2D,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFX,KAAK,EAAE,GAAI;MAAAO,QAAA,EAEVxD,cAAc,iBACbpB,OAAA;QAAA4E,QAAA,gBACE5E,OAAA,CAACf,OAAO;UAACqL,WAAW,EAAC,MAAM;UAAA1F,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC1ChF,OAAA,CAACpB,GAAG;UAACkI,MAAM,EAAE,EAAG;UAAAlC,QAAA,gBACd5E,OAAA,CAACnB,GAAG;YAACkI,IAAI,EAAE,EAAG;YAAAnC,QAAA,gBACZ5E,OAAA;cAAA4E,QAAA,gBAAG5E,OAAA;gBAAA4E,QAAA,EAAQ;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC5D,cAAc,CAACiE,EAAE;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/ChF,OAAA;cAAA4E,QAAA,gBAAG5E,OAAA;gBAAA4E,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,eAAAhF,OAAA,CAAC7B,GAAG;gBAAC8G,KAAK,EAAEtB,iBAAiB,CAACvC,cAAc,CAACmJ,UAAU,CAAE;gBAAA3F,QAAA,EAAExD,cAAc,CAACmJ;cAAU;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzHhF,OAAA;cAAA4E,QAAA,gBAAG5E,OAAA;gBAAA4E,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC5D,cAAc,CAACoJ,gBAAgB;YAAA;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DhF,OAAA;cAAA4E,QAAA,gBAAG5E,OAAA;gBAAA4E,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,CAAC,MAAM;gBACjC,MAAME,UAAU,GAAGxD,WAAW,CAACyD,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKjE,cAAc,CAACmC,aAAa,CAAC;gBACrF,OAAO2B,UAAU,gBACflF,OAAA,CAAC7B,GAAG;kBAAC8G,KAAK,EAAC,MAAM;kBAAAL,QAAA,GAAEM,UAAU,CAACM,IAAI,EAAC,IAAE,EAACN,UAAU,CAACI,OAAO,EAAC,GAAC;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,gBAEhEhF,OAAA,CAAC7B,GAAG;kBAAC8G,KAAK,EAAC,SAAS;kBAAAL,QAAA,GAAC,MAAI,EAACxD,cAAc,CAACmC,aAAa;gBAAA;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC7D;cACH,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNhF,OAAA,CAACnB,GAAG;YAACkI,IAAI,EAAE,EAAG;YAAAnC,QAAA,gBACZ5E,OAAA;cAAA4E,QAAA,gBAAG5E,OAAA;gBAAA4E,QAAA,EAAQ;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,CAAC5D,cAAc,CAACqJ,YAAY,GAAG,GAAG,EAAE3C,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9EhF,OAAA;cAAA4E,QAAA,gBAAG5E,OAAA;gBAAA4E,QAAA,EAAQ;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC5D,cAAc,CAAC2G,QAAQ,GAAG,GAAG,GAAG,GAAG;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClEhF,OAAA;cAAA4E,QAAA,gBAAG5E,OAAA;gBAAA4E,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,IAAIiD,IAAI,CAAC7G,cAAc,CAACsJ,UAAU,CAAC,CAACxC,cAAc,CAAC,CAAC;YAAA;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhF,OAAA,CAACf,OAAO;UAACqL,WAAW,EAAC,MAAM;UAAA1F,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACxChF,OAAA;UAAGwE,KAAK,EAAE;YAAEE,UAAU,EAAE,SAAS;YAAEC,OAAO,EAAE,MAAM;YAAEgG,YAAY,EAAE;UAAM,CAAE;UAAA/F,QAAA,EACvExD,cAAc,CAACkC;QAAQ;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAEJhF,OAAA,CAACf,OAAO;UAACqL,WAAW,EAAC,MAAM;UAAA1F,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC3ChF,OAAA;UAAKwE,KAAK,EAAE;YAAEE,UAAU,EAAE,SAAS;YAAEC,OAAO,EAAE,MAAM;YAAEgG,YAAY,EAAE,KAAK;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAhG,QAAA,EAC3FxD,cAAc,CAACmD;QAAG;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAENhF,OAAA,CAACf,OAAO;UAACqL,WAAW,EAAC,MAAM;UAAA1F,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC1ChF,OAAA;UAAA4E,QAAA,GAAAtE,qBAAA,GACGc,cAAc,CAACyJ,WAAW,cAAAvK,qBAAA,uBAA1BA,qBAAA,CAA4BuG,GAAG,CAAC,CAACiE,KAAK,EAAEC,KAAK,kBAC5C/K,OAAA,CAAC7B,GAAG;YAAa8G,KAAK,EAAC,MAAM;YAAAL,QAAA,EAAEkG;UAAK,GAA1BC,KAAK;YAAAlG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA2B,CAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENhF,OAAA,CAACf,OAAO;UAACqL,WAAW,EAAC,MAAM;UAAA1F,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC3ChF,OAAA;UAAA4E,QAAA,GAAArE,qBAAA,GACGa,cAAc,CAAC4J,kBAAkB,cAAAzK,qBAAA,uBAAjCA,qBAAA,CAAmCsG,GAAG,CAAC,CAACoE,MAAM,EAAEF,KAAK,kBACpD/K,OAAA,CAAC7B,GAAG;YAAa8G,KAAK,EAAC,OAAO;YAAAL,QAAA,EAAEqG;UAAM,GAA5BF,KAAK;YAAAlG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA6B,CAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRhF,OAAA,CAACF,eAAe;MACdoL,OAAO,EAAEhK,oBAAqB;MAC9B+I,QAAQ,EAAEA,CAAA,KAAM9I,uBAAuB,CAAC,KAAK,CAAE;MAC/CuC,MAAM,EAAEtC,cAAe;MACvB+J,mBAAmB,EAAEA,CAAA,KAAM;QACzB;QACA/I,SAAS,CAACR,oBAAoB,IAAIsB,SAAS,CAAC;MAC9C;IAAE;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3E,EAAA,CA1sBID,YAAsB;EAAA,QAaX/B,IAAI,CAAC4D,OAAO,EACN5D,IAAI,CAAC4D,OAAO;AAAA;AAAAmJ,EAAA,GAd7BhL,YAAsB;AA4sB5B,eAAeA,YAAY;AAAC,IAAAgL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}