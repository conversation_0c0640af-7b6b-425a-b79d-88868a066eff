import React, { createContext, useContext, useState, useEffect } from 'react';
import { Button, Switch, Slider, Select, Divider, Typography } from 'antd';
import { 
  EyeOutlined, 
  FontSizeOutlined, 
  BgColorsOutlined,
  SoundOutlined,
  SettingOutlined
} from '@ant-design/icons';
import '../styles/AccessibilityProvider.css';

const { Title, Text } = Typography;
const { Option } = Select;

interface AccessibilitySettings {
  fontSize: number;
  highContrast: boolean;
  reducedMotion: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
  colorBlindFriendly: boolean;
  theme: 'light' | 'dark' | 'high-contrast';
}

interface AccessibilityContextType {
  settings: AccessibilitySettings;
  updateSettings: (settings: Partial<AccessibilitySettings>) => void;
  resetSettings: () => void;
}

const defaultSettings: AccessibilitySettings = {
  fontSize: 16,
  highContrast: false,
  reducedMotion: false,
  screenReader: false,
  keyboardNavigation: true,
  colorBlindFriendly: false,
  theme: 'light'
};

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

export const useAccessibility = () => {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within AccessibilityProvider');
  }
  return context;
};

// 键盘导航Hook
export const useKeyboardNavigation = () => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Tab键导航增强
      if (event.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
      
      // Escape键关闭模态框
      if (event.key === 'Escape') {
        const activeModal = document.querySelector('.ant-modal');
        if (activeModal) {
          const closeButton = activeModal.querySelector('.ant-modal-close');
          if (closeButton) {
            (closeButton as HTMLElement).click();
          }
        }
      }
      
      // 快捷键支持
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case '/':
            event.preventDefault();
            // 聚焦到搜索框
            const searchInput = document.querySelector('input[placeholder*="搜索"], input[placeholder*="查询"]');
            if (searchInput) {
              (searchInput as HTMLElement).focus();
            }
            break;
          case 'k':
            event.preventDefault();
            // 打开命令面板
            console.log('Command palette shortcut');
            break;
        }
      }
    };

    const handleMouseDown = () => {
      document.body.classList.remove('keyboard-navigation');
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);
};

// 屏幕阅读器公告Hook
export const useScreenReader = () => {
  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  };

  return { announce };
};

export const AccessibilityProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [settings, setSettings] = useState<AccessibilitySettings>(() => {
    const saved = localStorage.getItem('accessibility-settings');
    return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
  });

  const updateSettings = (newSettings: Partial<AccessibilitySettings>) => {
    const updated = { ...settings, ...newSettings };
    setSettings(updated);
    localStorage.setItem('accessibility-settings', JSON.stringify(updated));
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    localStorage.removeItem('accessibility-settings');
  };

  // 应用设置到DOM
  useEffect(() => {
    const root = document.documentElement;
    
    // 字体大小
    root.style.fontSize = `${settings.fontSize}px`;
    
    // 主题
    root.setAttribute('data-theme', settings.theme);
    
    // 高对比度
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    // 减少动画
    if (settings.reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }
    
    // 色盲友好
    if (settings.colorBlindFriendly) {
      root.classList.add('color-blind-friendly');
    } else {
      root.classList.remove('color-blind-friendly');
    }
    
    // 键盘导航
    if (settings.keyboardNavigation) {
      root.classList.add('keyboard-navigation-enabled');
    } else {
      root.classList.remove('keyboard-navigation-enabled');
    }
  }, [settings]);

  return (
    <AccessibilityContext.Provider value={{ settings, updateSettings, resetSettings }}>
      {children}
    </AccessibilityContext.Provider>
  );
};

// 无障碍设置面板
export const AccessibilityPanel: React.FC<{ visible: boolean; onClose: () => void }> = ({ 
  visible, 
  onClose 
}) => {
  const { settings, updateSettings, resetSettings } = useAccessibility();

  if (!visible) return null;

  return (
    <div className="accessibility-panel" role="dialog" aria-labelledby="accessibility-title">
      <div className="accessibility-panel__content">
        <div className="accessibility-panel__header">
          <Title level={4} id="accessibility-title">
            <SettingOutlined /> 无障碍设置
          </Title>
          <Button type="text" onClick={onClose} aria-label="关闭设置面板">
            ×
          </Button>
        </div>
        
        <div className="accessibility-panel__body">
          <div className="accessibility-setting">
            <Text strong><FontSizeOutlined /> 字体大小</Text>
            <Slider
              min={12}
              max={24}
              value={settings.fontSize}
              onChange={(value) => updateSettings({ fontSize: value })}
              marks={{
                12: '小',
                16: '中',
                20: '大',
                24: '特大'
              }}
            />
          </div>

          <Divider />

          <div className="accessibility-setting">
            <Text strong><BgColorsOutlined /> 主题</Text>
            <Select
              value={settings.theme}
              onChange={(value) => updateSettings({ theme: value })}
              style={{ width: '100%' }}
            >
              <Option value="light">浅色主题</Option>
              <Option value="dark">深色主题</Option>
              <Option value="high-contrast">高对比度</Option>
            </Select>
          </div>

          <Divider />

          <div className="accessibility-setting">
            <div className="accessibility-switch">
              <Text>高对比度模式</Text>
              <Switch
                checked={settings.highContrast}
                onChange={(checked) => updateSettings({ highContrast: checked })}
              />
            </div>
          </div>

          <div className="accessibility-setting">
            <div className="accessibility-switch">
              <Text>减少动画效果</Text>
              <Switch
                checked={settings.reducedMotion}
                onChange={(checked) => updateSettings({ reducedMotion: checked })}
              />
            </div>
          </div>

          <div className="accessibility-setting">
            <div className="accessibility-switch">
              <Text>色盲友好模式</Text>
              <Switch
                checked={settings.colorBlindFriendly}
                onChange={(checked) => updateSettings({ colorBlindFriendly: checked })}
              />
            </div>
          </div>

          <div className="accessibility-setting">
            <div className="accessibility-switch">
              <Text>键盘导航增强</Text>
              <Switch
                checked={settings.keyboardNavigation}
                onChange={(checked) => updateSettings({ keyboardNavigation: checked })}
              />
            </div>
          </div>

          <div className="accessibility-setting">
            <div className="accessibility-switch">
              <Text>屏幕阅读器优化</Text>
              <Switch
                checked={settings.screenReader}
                onChange={(checked) => updateSettings({ screenReader: checked })}
              />
            </div>
          </div>
        </div>

        <div className="accessibility-panel__footer">
          <Button onClick={resetSettings}>重置设置</Button>
          <Button type="primary" onClick={onClose}>完成</Button>
        </div>
      </div>
    </div>
  );
};

// 跳转到主内容链接
export const SkipToContent: React.FC = () => (
  <a href="#main-content" className="skip-to-content">
    跳转到主内容
  </a>
);

export default AccessibilityProvider;
