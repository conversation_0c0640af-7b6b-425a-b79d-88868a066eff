#!/usr/bin/env python3
"""
调试BGE服务状态
"""

import asyncio
import httpx
import logging
import json

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_bge_service():
    """调试BGE服务"""
    bge_url = "http://localhost:8080"
    
    logger.info(f"🔍 开始调试BGE服务: {bge_url}")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 1. 测试健康检查
            logger.info("1️⃣ 测试健康检查...")
            try:
                response = await client.get(f"{bge_url}/health")
                logger.info(f"   状态码: {response.status_code}")
                logger.info(f"   响应头: {dict(response.headers)}")
                logger.info(f"   响应体: {response.text}")
                
                if response.status_code != 200:
                    logger.error(f"❌ 健康检查失败: {response.status_code}")
                    return False
                else:
                    logger.info("✅ 健康检查通过")
                    
            except Exception as e:
                logger.error(f"❌ 健康检查异常: {str(e)}")
                return False
            
            # 2. 测试根路径
            logger.info("2️⃣ 测试根路径...")
            try:
                response = await client.get(f"{bge_url}/")
                logger.info(f"   根路径状态码: {response.status_code}")
                logger.info(f"   根路径响应: {response.text[:200]}...")
            except Exception as e:
                logger.warning(f"⚠️ 根路径测试失败: {str(e)}")
            
            # 3. 测试embed接口 - 使用不同的请求格式
            logger.info("3️⃣ 测试embed接口...")
            
            test_formats = [
                {"inputs": "测试文本"},
                {"text": "测试文本"},
                {"input": "测试文本"},
                "测试文本"  # 直接字符串
            ]
            
            for i, test_data in enumerate(test_formats):
                logger.info(f"   测试格式 {i+1}: {test_data}")
                try:
                    if isinstance(test_data, str):
                        # 直接发送字符串
                        response = await client.post(
                            f"{bge_url}/embed",
                            content=test_data,
                            headers={"Content-Type": "text/plain"}
                        )
                    else:
                        # 发送JSON
                        response = await client.post(
                            f"{bge_url}/embed",
                            json=test_data,
                            headers={"Content-Type": "application/json"}
                        )
                    
                    logger.info(f"   格式 {i+1} 状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        logger.info(f"   ✅ 格式 {i+1} 成功! 响应类型: {type(result)}")
                        if isinstance(result, list) and result:
                            logger.info(f"   向量维度: {len(result[0]) if result[0] else 0}")
                        return True
                    elif response.status_code == 502:
                        logger.error(f"   ❌ 格式 {i+1} 返回502 Bad Gateway")
                        logger.error(f"   响应内容: {response.text}")
                    else:
                        logger.warning(f"   ⚠️ 格式 {i+1} 返回: {response.status_code}")
                        logger.warning(f"   响应内容: {response.text}")
                        
                except Exception as e:
                    logger.error(f"   ❌ 格式 {i+1} 异常: {str(e)}")
            
            # 4. 检查可用的端点
            logger.info("4️⃣ 检查可用端点...")
            endpoints = ["/", "/docs", "/openapi.json", "/metrics", "/info"]
            
            for endpoint in endpoints:
                try:
                    response = await client.get(f"{bge_url}{endpoint}")
                    logger.info(f"   {endpoint}: {response.status_code}")
                except Exception as e:
                    logger.warning(f"   {endpoint}: 异常 - {str(e)}")
            
            return False
            
    except Exception as e:
        logger.error(f"❌ 调试过程中出现错误: {str(e)}")
        return False

async def main():
    logger.info("🚀 开始BGE服务调试...")
    
    success = await debug_bge_service()
    
    if success:
        logger.info("🎉 BGE服务调试完成，找到可用的接口格式!")
    else:
        logger.error("💥 BGE服务调试失败，请检查服务状态")
        logger.info("💡 建议:")
        logger.info("   1. 确认BGE服务正在运行")
        logger.info("   2. 检查服务日志")
        logger.info("   3. 尝试重启BGE服务")
        logger.info("   4. 确认端口8080没有被其他服务占用")

if __name__ == "__main__":
    asyncio.run(main())
