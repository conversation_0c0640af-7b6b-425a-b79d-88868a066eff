import React, { useState, useEffect } from 'react';
import { Layout, But<PERSON>, Drawer } from 'antd';
import { MenuOutlined, CloseOutlined } from '@ant-design/icons';
import '../styles/ResponsiveLayout.css';

const { Header, Content, Sider } = Layout;

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  showSidebar?: boolean;
  sidebarWidth?: number;
  collapsible?: boolean;
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  sidebar,
  header,
  showSidebar = true,
  sidebarWidth = 260,
  collapsible = true
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const toggleSidebar = () => {
    if (isMobile) {
      setSidebarVisible(!sidebarVisible);
    } else {
      setCollapsed(!collapsed);
    }
  };

  const closeMobileSidebar = () => {
    setSidebarVisible(false);
  };

  if (isMobile) {
    return (
      <Layout className="responsive-layout mobile">
        {header && (
          <Header className="responsive-header">
            {showSidebar && (
              <Button
                type="text"
                icon={<MenuOutlined />}
                onClick={toggleSidebar}
                className="mobile-menu-button"
              />
            )}
            {header}
          </Header>
        )}
        
        <Content className="responsive-content">
          {children}
        </Content>

        {showSidebar && sidebar && (
          <Drawer
            title="菜单"
            placement="left"
            onClose={closeMobileSidebar}
            open={sidebarVisible}
            className="mobile-sidebar-drawer"
            width={280}
            closeIcon={<CloseOutlined />}
          >
            {sidebar}
          </Drawer>
        )}
      </Layout>
    );
  }

  return (
    <Layout className="responsive-layout desktop">
      {showSidebar && sidebar && (
        <Sider
          width={sidebarWidth}
          collapsed={collapsed}
          collapsible={collapsible}
          onCollapse={setCollapsed}
          className="responsive-sidebar"
          theme="light"
        >
          {sidebar}
        </Sider>
      )}
      
      <Layout className="responsive-main">
        {header && (
          <Header className="responsive-header">
            {header}
          </Header>
        )}
        
        <Content className="responsive-content">
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default ResponsiveLayout;
