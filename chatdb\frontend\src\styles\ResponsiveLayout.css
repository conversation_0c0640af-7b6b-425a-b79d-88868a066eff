/* 响应式布局样式 */

.responsive-layout {
  height: 100vh;
  overflow: hidden;
}

.responsive-layout.mobile {
  flex-direction: column;
}

.responsive-layout.desktop {
  flex-direction: row;
}

/* 头部样式 */
.responsive-header {
  background: linear-gradient(90deg, #001529 0%, #003a70 100%);
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1000;
  position: relative;
}

.mobile-menu-button {
  color: white;
  font-size: 18px;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.mobile-menu-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* 侧边栏样式 */
.responsive-sidebar {
  background: #f9f9f9;
  border-right: 1px solid #e8eaed;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.responsive-sidebar .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 移动端抽屉样式 */
.mobile-sidebar-drawer .ant-drawer-body {
  padding: 0;
  background: #f9f9f9;
}

.mobile-sidebar-drawer .ant-drawer-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-sidebar-drawer .ant-drawer-title {
  color: white;
  font-weight: 600;
}

.mobile-sidebar-drawer .ant-drawer-close {
  color: white;
}

.mobile-sidebar-drawer .ant-drawer-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 内容区域样式 */
.responsive-content {
  flex: 1;
  overflow: hidden;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.responsive-main {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .responsive-header {
    padding: 0 12px;
    height: 56px;
    min-height: 56px;
  }
  
  .responsive-content {
    padding: 0;
  }
  
  .mobile-menu-button {
    font-size: 16px;
    padding: 6px;
  }
}

/* 平板设备优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .responsive-sidebar {
    width: 240px !important;
    min-width: 240px !important;
    max-width: 240px !important;
  }
  
  .responsive-header {
    padding: 0 20px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .responsive-sidebar {
    width: 280px !important;
    min-width: 280px !important;
    max-width: 280px !important;
  }
  
  .responsive-content {
    padding: 0;
  }
}

/* 动画效果 */
.responsive-layout * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 确保内容区域正确滚动 */
.responsive-content > * {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

/* 侧边栏折叠状态优化 */
.ant-layout-sider-collapsed .responsive-sidebar-content {
  opacity: 0;
  transform: translateX(-20px);
}

.responsive-sidebar-content {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease;
}

/* 无障碍访问优化 */
.mobile-menu-button:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.responsive-layout button:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .responsive-header {
    background: #000000;
    border-bottom: 2px solid #ffffff;
  }
  
  .responsive-sidebar {
    background: #ffffff;
    border-right: 2px solid #000000;
  }
  
  .mobile-menu-button {
    border: 1px solid #ffffff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .responsive-layout *,
  .responsive-sidebar-content,
  .mobile-menu-button {
    transition: none;
  }
}
