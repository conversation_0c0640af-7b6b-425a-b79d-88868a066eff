/* 加载状态样式 */

/* 页面级加载 */
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.page-loading__content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-loading__tip {
  margin-top: 16px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

/* 内容区域加载 */
.content-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  min-height: 120px;
}

.content-loading--transparent {
  background: transparent;
}

/* 骨架屏样式 */
.skeleton-loading {
  padding: 16px;
}

.skeleton-loading--card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.skeleton-loading--list .skeleton-list-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-loading--list .skeleton-list-item:last-child {
  border-bottom: none;
}

.skeleton-loading--table {
  background: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.skeleton-loading--chat {
  padding: 0;
}

.skeleton-chat-message {
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 12px;
  max-width: 80%;
}

.skeleton-chat-message--user {
  margin-left: auto;
  background: #f0f2f5;
}

.skeleton-chat-message--bot {
  margin-right: auto;
  background: white;
  border: 1px solid #f0f0f0;
}

/* 进度加载 */
.progress-loading {
  padding: 16px;
  text-align: center;
}

/* 按钮加载 */
.button-loading {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.button-loading__icon {
  animation: spin 1s linear infinite;
}

.button-loading__text {
  transition: opacity 0.2s ease;
}

/* 懒加载 */
.lazy-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 暗色主题 */
[data-theme="dark"] .page-loading {
  background: rgba(20, 20, 20, 0.9);
}

[data-theme="dark"] .page-loading__content {
  background: #1f1f1f;
  border-color: #434343;
}

[data-theme="dark"] .content-loading {
  background: rgba(31, 31, 31, 0.8);
}

[data-theme="dark"] .skeleton-loading--card,
[data-theme="dark"] .skeleton-loading--table {
  background: #1f1f1f;
  border-color: #434343;
}

[data-theme="dark"] .skeleton-chat-message--user {
  background: #262626;
}

[data-theme="dark"] .skeleton-chat-message--bot {
  background: #1f1f1f;
  border-color: #434343;
}

[data-theme="dark"] .lazy-loading {
  background: #262626;
  border-color: #434343;
}

/* 高对比度模式 */
[data-theme="high-contrast"] .page-loading {
  background: rgba(255, 255, 255, 0.95);
}

[data-theme="high-contrast"] .page-loading__content {
  background: white;
  border: 2px solid black;
}

[data-theme="high-contrast"] .content-loading {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid black;
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .button-loading__icon {
    animation: none;
  }
  
  .page-loading__content,
  .content-loading,
  .skeleton-loading {
    transition: none;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-loading__content {
    padding: 24px;
    margin: 16px;
  }
  
  .skeleton-loading {
    padding: 12px;
  }
  
  .skeleton-chat-message {
    max-width: 90%;
    padding: 8px 12px;
  }
}
